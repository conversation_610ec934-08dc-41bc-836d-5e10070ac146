"""
نظام إدارة الصلاحيات
"""
from ..models.user import UserRole


# تعريف الصلاحيات
PERMISSIONS = {
    # صلاحيات المخاطر
    "RISKS_VIEW": "عرض المخاطر",
    "RISKS_CREATE": "إنشاء المخاطر",
    "RISKS_EDIT": "تعديل المخاطر",
    "RISKS_DELETE": "حذف المخاطر",
    "RISKS_ASSESS": "تقييم المخاطر",
    "RISKS_APPROVE": "الموافقة على المخاطر",
    
    # صلاحيات خطط التخفيف
    "MITIGATION_VIEW": "عرض خطط التخفيف",
    "MITIGATION_CREATE": "إنشاء خطط التخفيف",
    "MITIGATION_EDIT": "تعديل خطط التخفيف",
    "MITIGATION_DELETE": "حذف خطط التخفيف",
    "MITIGATION_APPROVE": "الموافقة على خطط التخفيف",
    
    # صلاحيات الحوادث
    "INCIDENTS_VIEW": "عرض الحوادث",
    "INCIDENTS_CREATE": "إنشاء الحوادث",
    "INCIDENTS_EDIT": "تعديل الحوادث",
    "INCIDENTS_DELETE": "حذف الحوادث",
    
    # صلاحيات المستخدمين
    "USERS_VIEW": "عرض المستخدمين",
    "USERS_CREATE": "إنشاء المستخدمين",
    "USERS_EDIT": "تعديل المستخدمين",
    "USERS_DELETE": "حذف المستخدمين",
    
    # صلاحيات الحوكمة
    "GOVERNANCE_VIEW": "عرض الحوكمة",
    "GOVERNANCE_MANAGE": "إدارة الحوكمة",
    
    # صلاحيات السياسات
    "POLICIES_VIEW": "عرض السياسات",
    "POLICIES_CREATE": "إنشاء السياسات",
    "POLICIES_EDIT": "تعديل السياسات",
    "POLICIES_DELETE": "حذف السياسات",
    "POLICIES_APPROVE": "الموافقة على السياسات",
    
    # صلاحيات الامتثال
    "COMPLIANCE_VIEW": "عرض الامتثال",
    "COMPLIANCE_MANAGE": "إدارة الامتثال",
    
    # صلاحيات المراجعة
    "AUDITS_VIEW": "عرض المراجعات",
    "AUDITS_CREATE": "إنشاء المراجعات",
    "AUDITS_EDIT": "تعديل المراجعات",
    "AUDITS_DELETE": "حذف المراجعات",
    
    # صلاحيات التقارير
    "REPORTS_VIEW": "عرض التقارير",
    "REPORTS_CREATE": "إنشاء التقارير",
    "REPORTS_EXPORT": "تصدير التقارير",
    
    # صلاحيات عامة
    "DASHBOARD_VIEW": "عرض لوحة التحكم",
    "PROFILE_EDIT": "تعديل الملف الشخصي",
    "SYSTEM_ADMIN": "إدارة النظام"
}


# خريطة الأدوار والصلاحيات
ROLE_PERMISSIONS = {
    UserRole.ADMIN: [
        # صلاحيات كاملة على كل شيء
        *PERMISSIONS.keys()
    ],
    
    UserRole.RISK_MANAGER: [
        # صلاحيات إدارة المخاطر
        "RISKS_VIEW",
        "RISKS_CREATE",
        "RISKS_EDIT",
        "RISKS_DELETE",
        "RISKS_ASSESS",
        "RISKS_APPROVE",
        
        # صلاحيات خطط التخفيف
        "MITIGATION_VIEW",
        "MITIGATION_CREATE",
        "MITIGATION_EDIT",
        "MITIGATION_DELETE",
        "MITIGATION_APPROVE",
        
        # صلاحيات عرض الحوادث
        "INCIDENTS_VIEW",
        "INCIDENTS_CREATE",
        "INCIDENTS_EDIT",
        
        # صلاحيات التقارير
        "REPORTS_VIEW",
        "REPORTS_CREATE",
        "REPORTS_EXPORT",
        
        # صلاحيات عامة
        "DASHBOARD_VIEW",
        "PROFILE_EDIT"
    ],
    
    UserRole.COMPLIANCE_OFFICER: [
        # صلاحيات الامتثال
        "COMPLIANCE_VIEW",
        "COMPLIANCE_MANAGE",
        
        # صلاحيات السياسات
        "POLICIES_VIEW",
        "POLICIES_CREATE",
        "POLICIES_EDIT",
        "POLICIES_DELETE",
        "POLICIES_APPROVE",
        
        # صلاحيات المراجعة
        "AUDITS_VIEW",
        "AUDITS_CREATE",
        "AUDITS_EDIT",
        "AUDITS_DELETE",
        
        # صلاحيات عرض المخاطر
        "RISKS_VIEW",
        "RISKS_ASSESS",
        
        # صلاحيات عرض الحوادث
        "INCIDENTS_VIEW",
        "INCIDENTS_CREATE",
        "INCIDENTS_EDIT",
        
        # صلاحيات التقارير
        "REPORTS_VIEW",
        "REPORTS_CREATE",
        "REPORTS_EXPORT",
        
        # صلاحيات عامة
        "DASHBOARD_VIEW",
        "GOVERNANCE_VIEW",
        "PROFILE_EDIT"
    ],
    
    UserRole.AUDITOR: [
        # صلاحيات المراجعة
        "AUDITS_VIEW",
        "AUDITS_CREATE",
        "AUDITS_EDIT",
        
        # صلاحيات عرض فقط
        "RISKS_VIEW",
        "MITIGATION_VIEW",
        "INCIDENTS_VIEW",
        "POLICIES_VIEW",
        "COMPLIANCE_VIEW",
        "GOVERNANCE_VIEW",
        
        # صلاحيات التقارير
        "REPORTS_VIEW",
        "REPORTS_CREATE",
        "REPORTS_EXPORT",
        
        # صلاحيات عامة
        "DASHBOARD_VIEW",
        "PROFILE_EDIT"
    ],
    
    UserRole.USER: [
        # صلاحيات أساسية فقط
        "DASHBOARD_VIEW",
        "INCIDENTS_VIEW",
        "INCIDENTS_CREATE",
        "RISKS_VIEW",
        "MITIGATION_VIEW",
        "GOVERNANCE_VIEW",
        "POLICIES_VIEW",
        "COMPLIANCE_VIEW",
        "REPORTS_VIEW",
        "PROFILE_EDIT"
    ]
}


def check_permission(user_role: UserRole, permission: str) -> bool:
    """
    التحقق من صلاحية المستخدم
    
    Args:
        user_role: دور المستخدم
        permission: الصلاحية المطلوبة
    
    Returns:
        bool: True إذا كان لديه الصلاحية، False إذا لم تكن لديه
    """
    if not user_role or not permission:
        return False
    
    role_permissions = ROLE_PERMISSIONS.get(user_role, [])
    return permission in role_permissions


def get_user_permissions(user_role: UserRole) -> list:
    """
    الحصول على جميع صلاحيات المستخدم
    
    Args:
        user_role: دور المستخدم
    
    Returns:
        list: قائمة بجميع الصلاحيات
    """
    return ROLE_PERMISSIONS.get(user_role, [])


def get_permission_description(permission: str) -> str:
    """
    الحصول على وصف الصلاحية
    
    Args:
        permission: اسم الصلاحية
    
    Returns:
        str: وصف الصلاحية
    """
    return PERMISSIONS.get(permission, permission)


def has_any_permission(user_role: UserRole, permissions: list) -> bool:
    """
    التحقق من وجود أي من الصلاحيات المحددة
    
    Args:
        user_role: دور المستخدم
        permissions: قائمة الصلاحيات للتحقق منها
    
    Returns:
        bool: True إذا كان لديه أي من الصلاحيات
    """
    user_permissions = get_user_permissions(user_role)
    return any(permission in user_permissions for permission in permissions)


def has_all_permissions(user_role: UserRole, permissions: list) -> bool:
    """
    التحقق من وجود جميع الصلاحيات المحددة
    
    Args:
        user_role: دور المستخدم
        permissions: قائمة الصلاحيات للتحقق منها
    
    Returns:
        bool: True إذا كان لديه جميع الصلاحيات
    """
    user_permissions = get_user_permissions(user_role)
    return all(permission in user_permissions for permission in permissions)


def is_admin(user_role: UserRole) -> bool:
    """
    التحقق من كون المستخدم مدير
    
    Args:
        user_role: دور المستخدم
    
    Returns:
        bool: True إذا كان مدير
    """
    return user_role == UserRole.ADMIN


def can_manage_users(user_role: UserRole) -> bool:
    """
    التحقق من إمكانية إدارة المستخدمين
    
    Args:
        user_role: دور المستخدم
    
    Returns:
        bool: True إذا كان يمكنه إدارة المستخدمين
    """
    return has_any_permission(user_role, ["USERS_CREATE", "USERS_EDIT", "USERS_DELETE"])


def can_manage_risks(user_role: UserRole) -> bool:
    """
    التحقق من إمكانية إدارة المخاطر
    
    Args:
        user_role: دور المستخدم
    
    Returns:
        bool: True إذا كان يمكنه إدارة المخاطر
    """
    return has_any_permission(user_role, ["RISKS_CREATE", "RISKS_EDIT", "RISKS_DELETE"])


def can_manage_compliance(user_role: UserRole) -> bool:
    """
    التحقق من إمكانية إدارة الامتثال
    
    Args:
        user_role: دور المستخدم
    
    Returns:
        bool: True إذا كان يمكنه إدارة الامتثال
    """
    return has_any_permission(user_role, ["COMPLIANCE_MANAGE", "POLICIES_CREATE", "AUDITS_CREATE"])
