"""
API endpoints للحوادث السيبرانية
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, extract
from ..database import get_db
from ..models.user import User, UserRole
from ..models.incident import Incident, IncidentType, IncidentSeverity, IncidentStatus
from ..schemas.incident import (
    Incident as IncidentSchema,
    IncidentCreate,
    IncidentUpdate,
    IncidentWithUser,
    IncidentSummary,
    IncidentStats
)
from ..core.deps import get_current_active_user, get_current_admin_user
from ..core.security import sanitize_input

router = APIRouter()


@router.get("/", response_model=List[IncidentWithUser])
async def read_incidents(
    skip: int = 0,
    limit: int = 100,
    incident_type: Optional[IncidentType] = None,
    severity: Optional[IncidentSeverity] = None,
    status: Optional[IncidentStatus] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على قائمة الحوادث
    """
    query = db.query(Incident).join(User, Incident.created_by == User.id)
    
    # المستخدمون العاديون يرون حوادثهم فقط، المديرون يرون جميع الحوادث
    if current_user.role != UserRole.ADMIN:
        query = query.filter(Incident.created_by == current_user.id)
    
    # تطبيق الفلاتر
    if incident_type:
        query = query.filter(Incident.incident_type == incident_type)
    
    if severity:
        query = query.filter(Incident.severity == severity)
    
    if status:
        query = query.filter(Incident.status == status)
    
    if search:
        search_term = f"%{sanitize_input(search)}%"
        query = query.filter(
            or_(
                Incident.title.ilike(search_term),
                Incident.description.ilike(search_term),
                Incident.reference_number.ilike(search_term)
            )
        )
    
    incidents = query.offset(skip).limit(limit).all()
    
    # إضافة معلومات المستخدم
    result = []
    for incident in incidents:
        incident_data = IncidentWithUser(
            **incident.__dict__,
            created_by_username=incident.created_by_user.username,
            created_by_full_name=incident.created_by_user.full_name
        )
        result.append(incident_data)
    
    return result


@router.get("/stats", response_model=IncidentStats)
async def get_incident_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على إحصائيات الحوادث
    """
    query = db.query(Incident)
    
    # المستخدمون العاديون يرون إحصائيات حوادثهم فقط
    if current_user.role != UserRole.ADMIN:
        query = query.filter(Incident.created_by == current_user.id)
    
    # إحصائيات عامة
    total_incidents = query.count()
    open_incidents = query.filter(Incident.status == IncidentStatus.OPEN).count()
    resolved_incidents = query.filter(Incident.status == IncidentStatus.RESOLVED).count()
    critical_incidents = query.filter(Incident.severity == IncidentSeverity.CRITICAL).count()
    
    # إحصائيات حسب النوع
    incidents_by_type = {}
    for incident_type in IncidentType:
        count = query.filter(Incident.incident_type == incident_type).count()
        if count > 0:
            incidents_by_type[incident_type.value] = count
    
    # إحصائيات حسب الشهر (آخر 12 شهر)
    incidents_by_month = {}
    current_year = datetime.now().year
    for month in range(1, 13):
        count = query.filter(
            and_(
                extract('year', Incident.incident_date) == current_year,
                extract('month', Incident.incident_date) == month
            )
        ).count()
        if count > 0:
            incidents_by_month[f"{current_year}-{month:02d}"] = count
    
    return IncidentStats(
        total_incidents=total_incidents,
        open_incidents=open_incidents,
        resolved_incidents=resolved_incidents,
        critical_incidents=critical_incidents,
        incidents_by_type=incidents_by_type,
        incidents_by_month=incidents_by_month
    )


@router.get("/{incident_id}", response_model=IncidentWithUser)
async def read_incident(
    incident_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على تفاصيل حادث محدد
    """
    incident = db.query(Incident).filter(Incident.id == incident_id).first()

    if not incident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحادث غير موجود"
        )

    # التحقق من الصلاحيات
    if current_user.role != UserRole.ADMIN and incident.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية للوصول إلى هذا الحادث"
        )

    return IncidentWithUser(
        **incident.__dict__,
        created_by_username=incident.created_by_user.username,
        created_by_full_name=incident.created_by_user.full_name
    )


@router.post("/", response_model=IncidentSchema)
async def create_incident(
    incident: IncidentCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    إنشاء حادث جديد
    """
    # تحويل البيانات إلى dict وتنظيف المدخلات
    incident_data = incident.dict()
    incident_data['title'] = sanitize_input(incident_data['title'])
    if incident_data.get('description'):
        incident_data['description'] = sanitize_input(incident_data['description'])

    # إنشاء رقم مرجعي فريد
    current_year = datetime.now().year
    incident_count = db.query(Incident).filter(
        extract('year', Incident.created_at) == current_year
    ).count() + 1
    reference_number = f"INC-{current_year}-{incident_count:04d}"

    # إنشاء الحادث
    db_incident = Incident(
        **incident_data,
        created_by=current_user.id,
        reference_number=reference_number
    )

    db.add(db_incident)
    db.commit()
    db.refresh(db_incident)

    return db_incident


@router.put("/{incident_id}", response_model=IncidentSchema)
async def update_incident(
    incident_id: int,
    incident_update: IncidentUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تحديث الحادث
    """
    incident = db.query(Incident).filter(Incident.id == incident_id).first()

    if not incident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحادث غير موجود"
        )

    # التحقق من الصلاحيات
    if current_user.role != UserRole.ADMIN and incident.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لتحديث هذا الحادث"
        )

    # تنظيف المدخلات
    update_data = incident_update.dict(exclude_unset=True)
    if 'title' in update_data:
        update_data['title'] = sanitize_input(update_data['title'])
    if 'description' in update_data:
        update_data['description'] = sanitize_input(update_data['description'])

    # تحديث تاريخ الحل إذا تم تغيير الحالة إلى محلول
    if incident_update.status == IncidentStatus.RESOLVED and incident.status != IncidentStatus.RESOLVED:
        update_data['resolved_date'] = datetime.now()
    elif incident_update.status != IncidentStatus.RESOLVED:
        update_data['resolved_date'] = None

    # تطبيق التحديثات
    for field, value in update_data.items():
        setattr(incident, field, value)

    db.commit()
    db.refresh(incident)

    return incident


@router.delete("/{incident_id}")
async def delete_incident(
    incident_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    حذف الحادث (للمديرين فقط)
    """
    incident = db.query(Incident).filter(Incident.id == incident_id).first()

    if not incident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحادث غير موجود"
        )

    db.delete(incident)
    db.commit()

    return {"message": "تم حذف الحادث بنجاح"}


@router.get("/summary/recent", response_model=List[IncidentSummary])
async def get_recent_incidents(
    limit: int = Query(10, le=50),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على ملخص الحوادث الأخيرة
    """
    query = db.query(Incident).join(User, Incident.created_by == User.id)

    # المستخدمون العاديون يرون حوادثهم فقط
    if current_user.role != UserRole.ADMIN:
        query = query.filter(Incident.created_by == current_user.id)

    incidents = query.order_by(Incident.created_at.desc()).limit(limit).all()

    result = []
    for incident in incidents:
        summary = IncidentSummary(
            id=incident.id,
            title=incident.title,
            incident_type=incident.incident_type,
            severity=incident.severity,
            status=incident.status,
            incident_date=incident.incident_date,
            created_by_username=incident.created_by_user.username
        )
        result.append(summary)

    return result
