// خدمات إدارة المخاطر
import api from './api'

// الحصول على قائمة المخاطر
export const getRisks = async (params = {}) => {
  try {
    const response = await api.get('/risks', { params })
    return response.data
  } catch (error) {
    console.error('خطأ في جلب المخاطر:', error)
    throw error
  }
}

// الحصول على تفاصيل مخاطرة محددة
export const getRisk = async (riskId) => {
  try {
    const response = await api.get(`/risks/${riskId}`)
    return response.data
  } catch (error) {
    console.error('خطأ في جلب تفاصيل المخاطرة:', error)
    throw error
  }
}

// إنشاء مخاطرة جديدة
export const createRisk = async (riskData) => {
  try {
    const response = await api.post('/risks', riskData)
    return response.data
  } catch (error) {
    console.error('خطأ في إنشاء المخاطرة:', error)
    throw error
  }
}

// تحديث مخاطرة موجودة
export const updateRisk = async (riskId, riskData) => {
  try {
    const response = await api.put(`/risks/${riskId}`, riskData)
    return response.data
  } catch (error) {
    console.error('خطأ في تحديث المخاطرة:', error)
    throw error
  }
}

// حذف مخاطرة
export const deleteRisk = async (riskId) => {
  try {
    const response = await api.delete(`/risks/${riskId}`)
    return response.data
  } catch (error) {
    console.error('خطأ في حذف المخاطرة:', error)
    throw error
  }
}

// تقييم مخاطرة
export const assessRisk = async (riskId, assessmentData) => {
  try {
    const response = await api.post(`/risks/${riskId}/assess`, assessmentData)
    return response.data
  } catch (error) {
    console.error('خطأ في تقييم المخاطرة:', error)
    throw error
  }
}

// الحصول على إحصائيات المخاطر
export const getRiskStatistics = async () => {
  try {
    const response = await api.get('/risks/statistics/overview')
    return response.data
  } catch (error) {
    console.error('خطأ في جلب إحصائيات المخاطر:', error)
    throw error
  }
}

// الحصول على بيانات مصفوفة المخاطر
export const getRiskMatrixData = async (params = {}) => {
  try {
    const response = await api.get('/risks/matrix/data', { params })
    return response.data
  } catch (error) {
    console.error('خطأ في جلب بيانات مصفوفة المخاطر:', error)
    throw error
  }
}

// الحصول على قائمة خطط التخفيف
export const getMitigationPlans = async (params = {}) => {
  try {
    const response = await api.get('/mitigation', { params })
    return response.data
  } catch (error) {
    console.error('خطأ في جلب خطط التخفيف:', error)
    throw error
  }
}

// الحصول على تفاصيل خطة تخفيف محددة
export const getMitigationPlan = async (planId) => {
  try {
    const response = await api.get(`/mitigation/${planId}`)
    return response.data
  } catch (error) {
    console.error('خطأ في جلب تفاصيل خطة التخفيف:', error)
    throw error
  }
}

// إنشاء خطة تخفيف جديدة
export const createMitigationPlan = async (planData) => {
  try {
    const response = await api.post('/mitigation', planData)
    return response.data
  } catch (error) {
    console.error('خطأ في إنشاء خطة التخفيف:', error)
    throw error
  }
}

// تحديث خطة تخفيف موجودة
export const updateMitigationPlan = async (planId, planData) => {
  try {
    const response = await api.put(`/mitigation/${planId}`, planData)
    return response.data
  } catch (error) {
    console.error('خطأ في تحديث خطة التخفيف:', error)
    throw error
  }
}

// حذف خطة تخفيف
export const deleteMitigationPlan = async (planId) => {
  try {
    const response = await api.delete(`/mitigation/${planId}`)
    return response.data
  } catch (error) {
    console.error('خطأ في حذف خطة التخفيف:', error)
    throw error
  }
}

// إضافة إجراء تخفيف جديد
export const createMitigationAction = async (planId, actionData) => {
  try {
    const response = await api.post(`/mitigation/${planId}/actions`, actionData)
    return response.data
  } catch (error) {
    console.error('خطأ في إضافة إجراء التخفيف:', error)
    throw error
  }
}

// تحديث إجراء تخفيف
export const updateMitigationAction = async (actionId, actionData) => {
  try {
    const response = await api.put(`/mitigation/actions/${actionId}`, actionData)
    return response.data
  } catch (error) {
    console.error('خطأ في تحديث إجراء التخفيف:', error)
    throw error
  }
}

// حذف إجراء تخفيف
export const deleteMitigationAction = async (actionId) => {
  try {
    const response = await api.delete(`/mitigation/actions/${actionId}`)
    return response.data
  } catch (error) {
    console.error('خطأ في حذف إجراء التخفيف:', error)
    throw error
  }
}

// الحصول على إحصائيات خطط التخفيف
export const getMitigationStatistics = async () => {
  try {
    const response = await api.get('/mitigation/statistics/overview')
    return response.data
  } catch (error) {
    console.error('خطأ في جلب إحصائيات خطط التخفيف:', error)
    throw error
  }
}

// دوال مساعدة لتحويل البيانات
export const formatRiskLevel = (level) => {
  const levels = {
    'VERY_LOW': 'منخفض جداً',
    'LOW': 'منخفض',
    'MEDIUM': 'متوسط',
    'HIGH': 'عالي',
    'VERY_HIGH': 'عالي جداً'
  }
  return levels[level] || level
}

export const formatRiskType = (type) => {
  const types = {
    'OPERATIONAL': 'تشغيلية',
    'FINANCIAL': 'مالية',
    'STRATEGIC': 'استراتيجية',
    'COMPLIANCE': 'امتثال',
    'REPUTATIONAL': 'سمعة',
    'TECHNOLOGY': 'تقنية',
    'CYBERSECURITY': 'أمن سيبراني',
    'LEGAL': 'قانونية',
    'ENVIRONMENTAL': 'بيئية',
    'MARKET': 'سوق'
  }
  return types[type] || type
}

export const formatRiskStatus = (status) => {
  const statuses = {
    'IDENTIFIED': 'محدد',
    'ASSESSED': 'مقيم',
    'MITIGATED': 'مخفف',
    'MONITORED': 'مراقب',
    'CLOSED': 'مغلق',
    'ESCALATED': 'مصعد'
  }
  return statuses[status] || status
}

export const formatMitigationStatus = (status) => {
  const statuses = {
    'PLANNED': 'مخطط',
    'IN_PROGRESS': 'قيد التنفيذ',
    'COMPLETED': 'مكتمل',
    'ON_HOLD': 'معلق',
    'CANCELLED': 'ملغي',
    'OVERDUE': 'متأخر'
  }
  return statuses[status] || status
}

export const getRiskLevelColor = (level) => {
  const colors = {
    'VERY_LOW': 'text-green-600 bg-green-100',
    'LOW': 'text-lime-600 bg-lime-100',
    'MEDIUM': 'text-yellow-600 bg-yellow-100',
    'HIGH': 'text-orange-600 bg-orange-100',
    'VERY_HIGH': 'text-red-600 bg-red-100'
  }
  return colors[level] || 'text-gray-600 bg-gray-100'
}

export const getMitigationStatusColor = (status) => {
  const colors = {
    'PLANNED': 'text-blue-600 bg-blue-100',
    'IN_PROGRESS': 'text-yellow-600 bg-yellow-100',
    'COMPLETED': 'text-green-600 bg-green-100',
    'ON_HOLD': 'text-gray-600 bg-gray-100',
    'CANCELLED': 'text-red-600 bg-red-100',
    'OVERDUE': 'text-red-700 bg-red-200'
  }
  return colors[status] || 'text-gray-600 bg-gray-100'
}
