import React, { useState } from 'react'
import { usersService } from '../../services/users'

const ErrorHandlingTest = () => {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const testDuplicateUser = async () => {
    setIsLoading(true)
    setResult('')
    
    try {
      // محاولة إنشاء مستخدم بنفس البيانات مرتين
      const userData = {
        username: 'testuser123',
        email: '<EMAIL>',
        full_name: 'Test User 123',
        password: 'TestPass123!',
        role: 'USER',
        is_active: true
      }

      console.log('Testing duplicate user creation...')
      const result = await usersService.createUser(userData)
      setResult(`✅ تم إنشاء المستخدم بنجاح: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      console.error('Caught error:', error)
      console.error('Error type:', typeof error)
      console.error('Error message:', error.message)
      console.error('Error toString:', error.toString())
      
      setResult(`❌ خطأ: ${error.message || error.toString() || 'خطأ غير معروف'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testNewUser = async () => {
    setIsLoading(true)
    setResult('')
    
    try {
      const randomId = Math.floor(Math.random() * 10000)
      const userData = {
        username: `newuser${randomId}`,
        email: `newuser${randomId}@example.com`,
        full_name: `New User ${randomId}`,
        password: 'NewPass123!',
        role: 'USER',
        is_active: true
      }

      console.log('Testing new user creation...')
      const result = await usersService.createUser(userData)
      setResult(`✅ تم إنشاء المستخدم بنجاح: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      console.error('Caught error:', error)
      setResult(`❌ خطأ: ${error.message || error.toString() || 'خطأ غير معروف'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testInvalidData = async () => {
    setIsLoading(true)
    setResult('')
    
    try {
      // بيانات غير صحيحة (بريد إلكتروني غير صحيح)
      const userData = {
        username: '',
        email: 'invalid-email',
        full_name: '',
        password: '123',
        role: 'USER',
        is_active: true
      }

      console.log('Testing invalid user data...')
      const result = await usersService.createUser(userData)
      setResult(`✅ تم إنشاء المستخدم بنجاح: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      console.error('Caught error:', error)
      setResult(`❌ خطأ: ${error.message || error.toString() || 'خطأ غير معروف'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 m-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        اختبار معالجة الأخطاء
      </h3>
      
      <div className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={testNewUser}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            {isLoading ? 'جاري...' : 'اختبار مستخدم جديد'}
          </button>
          
          <button
            onClick={testDuplicateUser}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
          >
            {isLoading ? 'جاري...' : 'اختبار مستخدم مكرر'}
          </button>
          
          <button
            onClick={testInvalidData}
            disabled={isLoading}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
          >
            {isLoading ? 'جاري...' : 'اختبار بيانات غير صحيحة'}
          </button>
        </div>

        {result && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">النتيجة:</h4>
            <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {result}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}

export default ErrorHandlingTest
