"""
APIs إدارة خطط التخفيف
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from typing import List, Optional
from datetime import datetime, timedelta

from ..database import get_db
from ..core.deps import get_current_user
from ..models.user import User, UserRole
from ..models.risk import Risk
from ..models.mitigation import MitigationPlan, MitigationAction, MitigationStatus
from ..schemas.mitigation import (
    MitigationPlanCreate, MitigationPlanUpdate, MitigationPlan as MitigationPlanSchema,
    MitigationPlanWithActions, MitigationPlanListResponse, MitigationStatistics,
    MitigationActionCreate, MitigationActionUpdate, MitigationAction as MitigationActionSchema
)
from ..utils.permissions import check_permission

router = APIRouter()


@router.get("/", response_model=MitigationPlanListResponse)
async def get_mitigation_plans(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    status: Optional[MitigationStatus] = Query(None),
    risk_id: Optional[int] = Query(None),
    owner_id: Optional[int] = Query(None),
    overdue_only: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة خطط التخفيف مع الفلترة والبحث"""
    
    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_VIEW"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لعرض خطط التخفيف")
    
    # بناء الاستعلام الأساسي
    query = db.query(MitigationPlan).options(
        joinedload(MitigationPlan.risk),
        joinedload(MitigationPlan.owner),
        joinedload(MitigationPlan.manager),
        joinedload(MitigationPlan.actions)
    )
    
    # تطبيق الفلاتر
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                MitigationPlan.title.ilike(search_term),
                MitigationPlan.description.ilike(search_term),
                MitigationPlan.objective.ilike(search_term)
            )
        )
    
    if status:
        query = query.filter(MitigationPlan.status == status)
    
    if risk_id:
        query = query.filter(MitigationPlan.risk_id == risk_id)
    
    if owner_id:
        query = query.filter(MitigationPlan.owner_id == owner_id)
    
    if overdue_only:
        current_date = datetime.utcnow()
        query = query.filter(
            and_(
                MitigationPlan.target_completion_date < current_date,
                MitigationPlan.status.notin_([MitigationStatus.COMPLETED, MitigationStatus.CANCELLED])
            )
        )
    
    # حساب العدد الإجمالي
    total = query.count()
    
    # تطبيق التصفح
    offset = (page - 1) * per_page
    plans = query.order_by(desc(MitigationPlan.created_at)).offset(offset).limit(per_page).all()
    
    # تحويل البيانات
    plans_data = []
    for plan in plans:
        plan_dict = MitigationPlanSchema.from_orm(plan).dict()
        
        # إضافة معلومات إضافية
        plan_dict['risk_title'] = plan.risk.title if plan.risk else None
        plan_dict['owner_name'] = plan.owner.full_name if plan.owner else None
        plan_dict['manager_name'] = plan.manager.full_name if plan.manager else None
        plan_dict['approved_by_name'] = plan.approved_by.full_name if plan.approved_by else None
        
        # إحصائيات الإجراءات
        plan_dict['actions_count'] = len(plan.actions)
        plan_dict['completed_actions'] = len([
            action for action in plan.actions 
            if action.status == MitigationStatus.COMPLETED
        ])
        plan_dict['overdue_actions'] = len([
            action for action in plan.actions 
            if action.is_overdue
        ])
        plan_dict['critical_actions'] = len([
            action for action in plan.actions 
            if action.is_critical
        ])
        plan_dict['is_overdue'] = plan.is_overdue
        
        plans_data.append(MitigationPlanSchema(**plan_dict))
    
    return MitigationPlanListResponse(
        data=plans_data,
        total=total,
        page=page,
        per_page=per_page
    )


@router.post("/", response_model=MitigationPlanSchema)
async def create_mitigation_plan(
    plan_data: MitigationPlanCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء خطة تخفيف جديدة"""
    
    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_CREATE"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لإنشاء خطط التخفيف")
    
    # التحقق من وجود المخاطرة
    risk = db.query(Risk).filter(Risk.id == plan_data.risk_id).first()
    if not risk:
        raise HTTPException(status_code=404, detail="المخاطرة المحددة غير موجودة")
    
    # التحقق من وجود المالك
    owner = db.query(User).filter(User.id == plan_data.owner_id).first()
    if not owner:
        raise HTTPException(status_code=404, detail="المالك المحدد غير موجود")
    
    # التحقق من وجود المدير (إذا تم تحديده)
    if plan_data.manager_id:
        manager = db.query(User).filter(User.id == plan_data.manager_id).first()
        if not manager:
            raise HTTPException(status_code=404, detail="المدير المحدد غير موجود")
    
    # إنشاء خطة التخفيف
    plan = MitigationPlan(**plan_data.dict())
    
    # تحديد تاريخ المراجعة التالية
    if not plan.next_review_date:
        plan.next_review_date = datetime.utcnow() + timedelta(days=plan.review_frequency)
    
    db.add(plan)
    db.commit()
    db.refresh(plan)
    
    return MitigationPlanSchema.from_orm(plan)


@router.get("/{plan_id}", response_model=MitigationPlanWithActions)
async def get_mitigation_plan(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على تفاصيل خطة تخفيف محددة مع الإجراءات"""
    
    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_VIEW"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لعرض خطط التخفيف")
    
    plan = db.query(MitigationPlan).options(
        joinedload(MitigationPlan.risk),
        joinedload(MitigationPlan.owner),
        joinedload(MitigationPlan.manager),
        joinedload(MitigationPlan.approved_by),
        joinedload(MitigationPlan.actions).joinedload(MitigationAction.responsible)
    ).filter(MitigationPlan.id == plan_id).first()
    
    if not plan:
        raise HTTPException(status_code=404, detail="خطة التخفيف غير موجودة")
    
    # تحويل البيانات
    plan_dict = MitigationPlanWithActions.from_orm(plan).dict()
    plan_dict['risk_title'] = plan.risk.title if plan.risk else None
    plan_dict['owner_name'] = plan.owner.full_name if plan.owner else None
    plan_dict['manager_name'] = plan.manager.full_name if plan.manager else None
    plan_dict['approved_by_name'] = plan.approved_by.full_name if plan.approved_by else None
    
    # تحويل الإجراءات
    actions_data = []
    for action in plan.actions:
        action_dict = MitigationActionSchema.from_orm(action).dict()
        action_dict['responsible_name'] = action.responsible.full_name if action.responsible else None
        action_dict['is_overdue'] = action.is_overdue
        action_dict['days_remaining'] = action.days_remaining
        actions_data.append(MitigationActionSchema(**action_dict))
    
    plan_dict['actions'] = actions_data
    plan_dict['actions_count'] = len(plan.actions)
    plan_dict['completed_actions'] = plan.completed_actions_count
    plan_dict['overdue_actions'] = plan.overdue_actions_count
    plan_dict['critical_actions'] = plan.critical_actions_count
    plan_dict['is_overdue'] = plan.is_overdue
    
    return MitigationPlanWithActions(**plan_dict)


@router.put("/{plan_id}", response_model=MitigationPlanSchema)
async def update_mitigation_plan(
    plan_id: int,
    plan_data: MitigationPlanUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث خطة تخفيف موجودة"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_EDIT"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لتعديل خطط التخفيف")

    plan = db.query(MitigationPlan).filter(MitigationPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="خطة التخفيف غير موجودة")

    # التحقق من وجود المدير الجديد (إذا تم تحديده)
    if plan_data.manager_id:
        manager = db.query(User).filter(User.id == plan_data.manager_id).first()
        if not manager:
            raise HTTPException(status_code=404, detail="المدير المحدد غير موجود")

    # تحديث البيانات
    update_data = plan_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(plan, field, value)

    # إعادة حساب نسبة الإنجاز
    plan.calculate_overall_progress()

    db.commit()
    db.refresh(plan)

    return MitigationPlanSchema.from_orm(plan)


@router.delete("/{plan_id}")
async def delete_mitigation_plan(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف خطة تخفيف"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_DELETE"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لحذف خطط التخفيف")

    plan = db.query(MitigationPlan).filter(MitigationPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="خطة التخفيف غير موجودة")

    # التحقق من وجود إجراءات قيد التنفيذ
    active_actions = db.query(MitigationAction).filter(
        and_(
            MitigationAction.mitigation_plan_id == plan_id,
            MitigationAction.status.in_([MitigationStatus.PLANNED, MitigationStatus.IN_PROGRESS])
        )
    ).count()

    if active_actions > 0:
        raise HTTPException(
            status_code=400,
            detail="لا يمكن حذف خطة التخفيف لوجود إجراءات قيد التنفيذ"
        )

    db.delete(plan)
    db.commit()

    return {"message": "تم حذف خطة التخفيف بنجاح"}


# APIs إدارة إجراءات التخفيف
@router.post("/{plan_id}/actions", response_model=MitigationActionSchema)
async def create_mitigation_action(
    plan_id: int,
    action_data: MitigationActionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إضافة إجراء تخفيف جديد لخطة"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_EDIT"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لتعديل خطط التخفيف")

    # التحقق من وجود الخطة
    plan = db.query(MitigationPlan).filter(MitigationPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="خطة التخفيف غير موجودة")

    # التحقق من وجود المسؤول
    responsible = db.query(User).filter(User.id == action_data.responsible_id).first()
    if not responsible:
        raise HTTPException(status_code=404, detail="المسؤول المحدد غير موجود")

    # إنشاء الإجراء
    action_data.mitigation_plan_id = plan_id
    action = MitigationAction(**action_data.dict())

    db.add(action)
    db.commit()
    db.refresh(action)

    # إعادة حساب نسبة الإنجاز للخطة
    plan.calculate_overall_progress()
    db.commit()

    return MitigationActionSchema.from_orm(action)


@router.put("/actions/{action_id}", response_model=MitigationActionSchema)
async def update_mitigation_action(
    action_id: int,
    action_data: MitigationActionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث إجراء تخفيف"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_EDIT"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لتعديل خطط التخفيف")

    action = db.query(MitigationAction).filter(MitigationAction.id == action_id).first()
    if not action:
        raise HTTPException(status_code=404, detail="إجراء التخفيف غير موجود")

    # التحقق من وجود المسؤول الجديد (إذا تم تحديده)
    if action_data.responsible_id:
        responsible = db.query(User).filter(User.id == action_data.responsible_id).first()
        if not responsible:
            raise HTTPException(status_code=404, detail="المسؤول المحدد غير موجود")

    # تحديث البيانات
    update_data = action_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(action, field, value)

    # تحديث تاريخ الإكمال إذا تم تغيير الحالة إلى مكتمل
    if action_data.status == MitigationStatus.COMPLETED and not action.completion_date:
        action.completion_date = datetime.utcnow()
        action.progress_percentage = 100.0

    db.commit()
    db.refresh(action)

    # إعادة حساب نسبة الإنجاز للخطة
    plan = db.query(MitigationPlan).filter(MitigationPlan.id == action.mitigation_plan_id).first()
    if plan:
        plan.calculate_overall_progress()
        db.commit()

    return MitigationActionSchema.from_orm(action)


@router.delete("/actions/{action_id}")
async def delete_mitigation_action(
    action_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف إجراء تخفيف"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_DELETE"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لحذف إجراءات التخفيف")

    action = db.query(MitigationAction).filter(MitigationAction.id == action_id).first()
    if not action:
        raise HTTPException(status_code=404, detail="إجراء التخفيف غير موجود")

    plan_id = action.mitigation_plan_id

    db.delete(action)
    db.commit()

    # إعادة حساب نسبة الإنجاز للخطة
    plan = db.query(MitigationPlan).filter(MitigationPlan.id == plan_id).first()
    if plan:
        plan.calculate_overall_progress()
        db.commit()

    return {"message": "تم حذف إجراء التخفيف بنجاح"}


@router.get("/statistics/overview", response_model=MitigationStatistics)
async def get_mitigation_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على إحصائيات خطط التخفيف"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "MITIGATION_VIEW"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لعرض خطط التخفيف")

    # العدد الإجمالي للخطط
    total_plans = db.query(MitigationPlan).count()

    # حسب الحالة
    by_status = {}
    for status in MitigationStatus:
        count = db.query(MitigationPlan).filter(MitigationPlan.status == status).count()
        by_status[status.value] = count

    # حسب الاستراتيجية
    strategies = ["تجنب", "تقليل", "نقل", "قبول"]
    by_strategy = {}
    for strategy in strategies:
        count = db.query(MitigationPlan).filter(MitigationPlan.strategy == strategy).count()
        by_strategy[strategy] = count

    # الخطط المتأخرة
    current_date = datetime.utcnow()
    overdue_plans = db.query(MitigationPlan).filter(
        and_(
            MitigationPlan.target_completion_date < current_date,
            MitigationPlan.status.notin_([MitigationStatus.COMPLETED, MitigationStatus.CANCELLED])
        )
    ).count()

    # الخطط المكتملة
    completed_plans = db.query(MitigationPlan).filter(
        MitigationPlan.status == MitigationStatus.COMPLETED
    ).count()

    # إحصائيات الإجراءات
    total_actions = db.query(MitigationAction).count()

    overdue_actions = db.query(MitigationAction).filter(
        and_(
            MitigationAction.due_date < current_date,
            MitigationAction.status.notin_([MitigationStatus.COMPLETED, MitigationStatus.CANCELLED])
        )
    ).count()

    critical_actions = db.query(MitigationAction).filter(
        MitigationAction.is_critical == True
    ).count()

    # متوسط نسبة الإنجاز
    avg_progress_result = db.query(func.avg(MitigationPlan.overall_progress)).scalar()
    average_progress = float(avg_progress_result) if avg_progress_result else 0.0

    return MitigationStatistics(
        total_plans=total_plans,
        by_status=by_status,
        by_strategy=by_strategy,
        overdue_plans=overdue_plans,
        completed_plans=completed_plans,
        total_actions=total_actions,
        overdue_actions=overdue_actions,
        critical_actions=critical_actions,
        average_progress=average_progress
    )
