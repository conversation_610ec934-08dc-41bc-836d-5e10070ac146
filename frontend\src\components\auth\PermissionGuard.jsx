import React from 'react'
import { hasPermission, hasAnyPermission, hasAllPermissions } from '../../utils/permissions'

/**
 * مكون للتحكم في عرض المحتوى بناءً على الصلاحيات
 */
const PermissionGuard = ({ 
  children, 
  userRole, 
  permission, 
  permissions, 
  requireAll = false,
  fallback = null 
}) => {
  // التحقق من وجود دور المستخدم
  if (!userRole) {
    return fallback
  }

  let hasAccess = false

  // التحقق من صلاحية واحدة
  if (permission) {
    hasAccess = hasPermission(userRole, permission)
  }
  // التحقق من صلاحيات متعددة
  else if (permissions && Array.isArray(permissions)) {
    if (requireAll) {
      hasAccess = hasAllPermissions(userRole, permissions)
    } else {
      hasAccess = hasAnyPermission(userRole, permissions)
    }
  }
  // إذا لم يتم تحديد صلاحيات، السماح بالوصول
  else {
    hasAccess = true
  }

  return hasAccess ? children : fallback
}

export default PermissionGuard

/**
 * Hook للتحقق من الصلاحيات
 */
export const usePermissions = (userRole) => {
  return {
    hasPermission: (permission) => hasPermission(userRole, permission),
    hasAnyPermission: (permissions) => hasAnyPermission(userRole, permissions),
    hasAllPermissions: (permissions) => hasAllPermissions(userRole, permissions),
    canAccess: (permission) => hasPermission(userRole, permission)
  }
}

/**
 * مكون للأزرار المشروطة بالصلاحيات
 */
export const PermissionButton = ({ 
  children, 
  userRole, 
  permission, 
  permissions, 
  requireAll = false,
  className = '',
  disabled = false,
  ...props 
}) => {
  const hasAccess = permission 
    ? hasPermission(userRole, permission)
    : permissions 
      ? (requireAll ? hasAllPermissions(userRole, permissions) : hasAnyPermission(userRole, permissions))
      : true

  if (!hasAccess) {
    return null
  }

  return (
    <button 
      className={className}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  )
}

/**
 * مكون للروابط المشروطة بالصلاحيات
 */
export const PermissionLink = ({ 
  children, 
  userRole, 
  permission, 
  permissions, 
  requireAll = false,
  className = '',
  ...props 
}) => {
  const hasAccess = permission 
    ? hasPermission(userRole, permission)
    : permissions 
      ? (requireAll ? hasAllPermissions(userRole, permissions) : hasAnyPermission(userRole, permissions))
      : true

  if (!hasAccess) {
    return null
  }

  return (
    <a className={className} {...props}>
      {children}
    </a>
  )
}
