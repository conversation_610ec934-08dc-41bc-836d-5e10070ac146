// نظام الصلاحيات المتقدم

// تعريف الأدوار والصلاحيات
export const ROLES = {
  ADMIN: 'ADMIN',
  RISK_MANAGER: 'RISK_MANAGER',
  COMPLIANCE_OFFICER: 'COMPLIANCE_OFFICER',
  AUDITOR: 'AUDITOR',
  USER: 'USER'
}

// تعريف الصلاحيات
export const PERMISSIONS = {
  // صلاحيات إدارة المستخدمين
  USERS_VIEW: 'users:view',
  USERS_CREATE: 'users:create',
  USERS_EDIT: 'users:edit',
  USERS_DELETE: 'users:delete',
  USERS_ACTIVATE: 'users:activate',
  
  // صلاحيات إدارة الحوادث
  INCIDENTS_VIEW: 'incidents:view',
  INCIDENTS_CREATE: 'incidents:create',
  INCIDENTS_EDIT: 'incidents:edit',
  INCIDENTS_DELETE: 'incidents:delete',
  INCIDENTS_ASSIGN: 'incidents:assign',
  INCIDENTS_CLOSE: 'incidents:close',
  
  // صلاحيات إدارة المخاطر
  RISKS_VIEW: 'risks:view',
  RISKS_CREATE: 'risks:create',
  RISKS_EDIT: 'risks:edit',
  RISKS_DELETE: 'risks:delete',
  RISKS_ASSESS: 'risks:assess',
  RISKS_APPROVE: 'risks:approve',
  
  // صلاحيات الحوكمة والامتثال
  GOVERNANCE_VIEW: 'governance:view',
  POLICIES_VIEW: 'policies:view',
  POLICIES_CREATE: 'policies:create',
  POLICIES_EDIT: 'policies:edit',
  POLICIES_DELETE: 'policies:delete',
  POLICIES_APPROVE: 'policies:approve',
  
  COMPLIANCE_VIEW: 'compliance:view',
  COMPLIANCE_CREATE: 'compliance:create',
  COMPLIANCE_EDIT: 'compliance:edit',
  COMPLIANCE_DELETE: 'compliance:delete',
  COMPLIANCE_ASSESS: 'compliance:assess',
  
  AUDITS_VIEW: 'audits:view',
  AUDITS_CREATE: 'audits:create',
  AUDITS_EDIT: 'audits:edit',
  AUDITS_DELETE: 'audits:delete',
  AUDITS_CONDUCT: 'audits:conduct',
  
  // صلاحيات التقارير
  REPORTS_VIEW: 'reports:view',
  REPORTS_CREATE: 'reports:create',
  REPORTS_EXPORT: 'reports:export',
  REPORTS_ADVANCED: 'reports:advanced',
  
  // صلاحيات النظام
  SYSTEM_SETTINGS: 'system:settings',
  SYSTEM_LOGS: 'system:logs',
  SYSTEM_BACKUP: 'system:backup',
  
  // صلاحيات عامة
  DASHBOARD_VIEW: 'dashboard:view',
  PROFILE_EDIT: 'profile:edit'
}

// خريطة الأدوار والصلاحيات
export const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [
    // صلاحيات كاملة على كل شيء
    ...Object.values(PERMISSIONS)
  ],
  
  [ROLES.RISK_MANAGER]: [
    // صلاحيات إدارة المخاطر
    PERMISSIONS.RISKS_VIEW,
    PERMISSIONS.RISKS_CREATE,
    PERMISSIONS.RISKS_EDIT,
    PERMISSIONS.RISKS_DELETE,
    PERMISSIONS.RISKS_ASSESS,
    PERMISSIONS.RISKS_APPROVE,
    
    // صلاحيات عرض الحوادث
    PERMISSIONS.INCIDENTS_VIEW,
    PERMISSIONS.INCIDENTS_CREATE,
    PERMISSIONS.INCIDENTS_EDIT,
    
    // صلاحيات التقارير
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_CREATE,
    PERMISSIONS.REPORTS_EXPORT,
    
    // صلاحيات عامة
    PERMISSIONS.DASHBOARD_VIEW,
    PERMISSIONS.PROFILE_EDIT
  ],
  
  [ROLES.COMPLIANCE_OFFICER]: [
    // صلاحيات الحوكمة والامتثال
    PERMISSIONS.GOVERNANCE_VIEW,
    PERMISSIONS.POLICIES_VIEW,
    PERMISSIONS.POLICIES_CREATE,
    PERMISSIONS.POLICIES_EDIT,
    PERMISSIONS.POLICIES_APPROVE,
    
    PERMISSIONS.COMPLIANCE_VIEW,
    PERMISSIONS.COMPLIANCE_CREATE,
    PERMISSIONS.COMPLIANCE_EDIT,
    PERMISSIONS.COMPLIANCE_ASSESS,
    
    // صلاحيات عرض المخاطر والحوادث
    PERMISSIONS.RISKS_VIEW,
    PERMISSIONS.INCIDENTS_VIEW,
    
    // صلاحيات التقارير
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_CREATE,
    PERMISSIONS.REPORTS_EXPORT,
    
    // صلاحيات عامة
    PERMISSIONS.DASHBOARD_VIEW,
    PERMISSIONS.PROFILE_EDIT
  ],
  
  [ROLES.AUDITOR]: [
    // صلاحيات التدقيق
    PERMISSIONS.AUDITS_VIEW,
    PERMISSIONS.AUDITS_CREATE,
    PERMISSIONS.AUDITS_EDIT,
    PERMISSIONS.AUDITS_CONDUCT,
    
    // صلاحيات عرض فقط للمخاطر والحوادث والامتثال
    PERMISSIONS.RISKS_VIEW,
    PERMISSIONS.INCIDENTS_VIEW,
    PERMISSIONS.GOVERNANCE_VIEW,
    PERMISSIONS.POLICIES_VIEW,
    PERMISSIONS.COMPLIANCE_VIEW,
    
    // صلاحيات التقارير
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_CREATE,
    PERMISSIONS.REPORTS_EXPORT,
    
    // صلاحيات عامة
    PERMISSIONS.DASHBOARD_VIEW,
    PERMISSIONS.PROFILE_EDIT
  ],
  
  [ROLES.USER]: [
    // صلاحيات أساسية فقط
    PERMISSIONS.DASHBOARD_VIEW,
    PERMISSIONS.INCIDENTS_VIEW,
    PERMISSIONS.INCIDENTS_CREATE,
    PERMISSIONS.RISKS_VIEW,
    PERMISSIONS.GOVERNANCE_VIEW,
    PERMISSIONS.POLICIES_VIEW,
    PERMISSIONS.COMPLIANCE_VIEW,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.PROFILE_EDIT
  ]
}

// دالة للتحقق من الصلاحيات
export const hasPermission = (userRole, permission) => {
  if (!userRole || !permission) return false
  
  const rolePermissions = ROLE_PERMISSIONS[userRole] || []
  return rolePermissions.includes(permission)
}

// دالة للتحقق من صلاحيات متعددة
export const hasAnyPermission = (userRole, permissions) => {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false
  
  return permissions.some(permission => hasPermission(userRole, permission))
}

// دالة للتحقق من جميع الصلاحيات
export const hasAllPermissions = (userRole, permissions) => {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false
  
  return permissions.every(permission => hasPermission(userRole, permission))
}

// دالة للحصول على جميع صلاحيات المستخدم
export const getUserPermissions = (userRole) => {
  if (!userRole) return []
  
  return ROLE_PERMISSIONS[userRole] || []
}

// دالة للتحقق من صلاحية الوصول للصفحة
export const canAccessPage = (userRole, page) => {
  const pagePermissions = {
    '/users': [PERMISSIONS.USERS_VIEW],
    '/users/create': [PERMISSIONS.USERS_CREATE],
    '/users/edit': [PERMISSIONS.USERS_EDIT],
    '/incidents': [PERMISSIONS.INCIDENTS_VIEW],
    '/incidents/create': [PERMISSIONS.INCIDENTS_CREATE],
    '/incidents/edit': [PERMISSIONS.INCIDENTS_EDIT],
    '/risks': [PERMISSIONS.RISKS_VIEW],
    '/risks/create': [PERMISSIONS.RISKS_CREATE],
    '/risks/edit': [PERMISSIONS.RISKS_EDIT],
    '/governance': [PERMISSIONS.GOVERNANCE_VIEW],
    '/policies': [PERMISSIONS.POLICIES_VIEW],
    '/compliance': [PERMISSIONS.COMPLIANCE_VIEW],
    '/audits': [PERMISSIONS.AUDITS_VIEW],
    '/reports': [PERMISSIONS.REPORTS_VIEW],
    '/settings': [PERMISSIONS.SYSTEM_SETTINGS]
  }
  
  const requiredPermissions = pagePermissions[page]
  if (!requiredPermissions) return true // إذا لم تكن هناك صلاحيات محددة، السماح بالوصول
  
  return hasAnyPermission(userRole, requiredPermissions)
}

// أسماء الأدوار باللغة العربية
export const ROLE_NAMES = {
  [ROLES.ADMIN]: 'مدير النظام',
  [ROLES.RISK_MANAGER]: 'مدير المخاطر',
  [ROLES.COMPLIANCE_OFFICER]: 'مسؤول الامتثال',
  [ROLES.AUDITOR]: 'مدقق',
  [ROLES.USER]: 'مستخدم'
}

// أوصاف الأدوار
export const ROLE_DESCRIPTIONS = {
  [ROLES.ADMIN]: 'صلاحيات كاملة لإدارة النظام والمستخدمين',
  [ROLES.RISK_MANAGER]: 'إدارة المخاطر وتقييمها ومتابعتها',
  [ROLES.COMPLIANCE_OFFICER]: 'إدارة الامتثال والسياسات والحوكمة',
  [ROLES.AUDITOR]: 'إجراء المراجعات والتدقيق الداخلي',
  [ROLES.USER]: 'صلاحيات أساسية للعرض والإبلاغ'
}

// ألوان الأدوار للواجهة
export const ROLE_COLORS = {
  [ROLES.ADMIN]: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  [ROLES.RISK_MANAGER]: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  [ROLES.COMPLIANCE_OFFICER]: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  [ROLES.AUDITOR]: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  [ROLES.USER]: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}
