import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronLeftIcon, HomeIcon } from '@heroicons/react/24/outline'
import { ROUTES } from '../../utils/constants'

const Breadcrumb = () => {
  const location = useLocation()
  
  // تعريف أسماء الصفحات
  const pageNames = {
    [ROUTES.DASHBOARD]: 'لوحة التحكم',
    [ROUTES.INCIDENTS]: 'الحوادث السيبرانية',
    [ROUTES.INCIDENT_CREATE]: 'إضافة حادث جديد',
    [ROUTES.INCIDENT_EDIT]: 'تعديل الحادث',
    [ROUTES.USERS]: 'إدارة المستخدمين',
    [ROUTES.USER_CREATE]: 'إضافة مستخدم جديد',
    [ROUTES.USER_EDIT]: 'تعديل المستخدم',
    [ROUTES.RISKS]: 'إدارة المخاطر',
    [ROUTES.RISK_CREATE]: 'إضافة مخاطرة جديدة',
    [ROUTES.RISK_EDIT]: 'تعديل المخاطرة',
    [ROUTES.RISK_MATRIX]: 'مصفوفة المخاطر',
    [ROUTES.RISK_ASSESSMENT]: 'تقييم المخاطر',
    [ROUTES.GOVERNANCE]: 'الحوكمة والامتثال',
    [ROUTES.POLICIES]: 'السياسات والإجراءات',
    [ROUTES.POLICY_CREATE]: 'إضافة سياسة جديدة',
    [ROUTES.POLICY_EDIT]: 'تعديل السياسة',
    [ROUTES.COMPLIANCE]: 'إدارة الامتثال',
    [ROUTES.COMPLIANCE_CREATE]: 'إضافة متطلب امتثال',
    [ROUTES.AUDITS]: 'المراجعات والتدقيق',
    [ROUTES.AUDIT_CREATE]: 'إضافة مراجعة جديدة',
    [ROUTES.REPORTS]: 'التقارير والتحليلات',
    [ROUTES.REPORTS_INCIDENTS]: 'تقارير الحوادث',
    [ROUTES.REPORTS_RISKS]: 'تقارير المخاطر',
    [ROUTES.REPORTS_COMPLIANCE]: 'تقارير الامتثال',
    [ROUTES.REPORTS_DASHBOARD]: 'لوحة التقارير',
    [ROUTES.PROFILE]: 'الملف الشخصي',
    [ROUTES.SETTINGS]: 'الإعدادات',
    [ROUTES.SYSTEM_SETTINGS]: 'إعدادات النظام'
  }

  // تعريف المسارات الأساسية
  const parentPaths = {
    [ROUTES.INCIDENT_CREATE]: ROUTES.INCIDENTS,
    [ROUTES.INCIDENT_EDIT]: ROUTES.INCIDENTS,
    [ROUTES.USER_CREATE]: ROUTES.USERS,
    [ROUTES.USER_EDIT]: ROUTES.USERS,
    [ROUTES.RISK_CREATE]: ROUTES.RISKS,
    [ROUTES.RISK_EDIT]: ROUTES.RISKS,
    [ROUTES.RISK_MATRIX]: ROUTES.RISKS,
    [ROUTES.RISK_ASSESSMENT]: ROUTES.RISKS,
    [ROUTES.POLICIES]: ROUTES.GOVERNANCE,
    [ROUTES.POLICY_CREATE]: ROUTES.POLICIES,
    [ROUTES.POLICY_EDIT]: ROUTES.POLICIES,
    [ROUTES.COMPLIANCE]: ROUTES.GOVERNANCE,
    [ROUTES.COMPLIANCE_CREATE]: ROUTES.COMPLIANCE,
    [ROUTES.AUDITS]: ROUTES.GOVERNANCE,
    [ROUTES.AUDIT_CREATE]: ROUTES.AUDITS,
    [ROUTES.REPORTS_INCIDENTS]: ROUTES.REPORTS,
    [ROUTES.REPORTS_RISKS]: ROUTES.REPORTS,
    [ROUTES.REPORTS_COMPLIANCE]: ROUTES.REPORTS,
    [ROUTES.REPORTS_DASHBOARD]: ROUTES.REPORTS,
    [ROUTES.SYSTEM_SETTINGS]: ROUTES.SETTINGS
  }

  // بناء مسار التنقل
  const buildBreadcrumbs = () => {
    const breadcrumbs = []
    let currentPath = location.pathname

    // إضافة الصفحة الرئيسية دائماً
    breadcrumbs.push({
      name: 'الرئيسية',
      path: ROUTES.DASHBOARD,
      icon: HomeIcon,
      current: currentPath === ROUTES.DASHBOARD
    })

    // إذا لم نكن في الصفحة الرئيسية
    if (currentPath !== ROUTES.DASHBOARD) {
      const pathsToAdd = []
      
      // إضافة المسارات الأساسية
      while (currentPath && parentPaths[currentPath]) {
        pathsToAdd.unshift(parentPaths[currentPath])
        currentPath = parentPaths[currentPath]
      }
      
      // إضافة المسار الحالي
      pathsToAdd.push(location.pathname)
      
      // إضافة جميع المسارات للـ breadcrumbs
      pathsToAdd.forEach((path, index) => {
        if (pageNames[path] && !breadcrumbs.find(b => b.path === path)) {
          breadcrumbs.push({
            name: pageNames[path],
            path: path,
            current: index === pathsToAdd.length - 1
          })
        }
      })
    }

    return breadcrumbs
  }

  const breadcrumbs = buildBreadcrumbs()

  // إذا كان هناك عنصر واحد فقط (الصفحة الرئيسية)، لا نعرض شيئاً
  if (breadcrumbs.length <= 1) {
    return null
  }

  return (
    <nav className="flex mb-6" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.path} className="inline-flex items-center">
            {index > 0 && (
              <ChevronLeftIcon className="w-4 h-4 text-dark-400 mx-2" />
            )}
            
            {breadcrumb.current ? (
              <span className="flex items-center text-sm font-medium text-primary-400">
                {breadcrumb.icon && (
                  <breadcrumb.icon className="w-4 h-4 ml-2" />
                )}
                {breadcrumb.name}
              </span>
            ) : (
              <Link
                to={breadcrumb.path}
                className="flex items-center text-sm font-medium text-dark-300 hover:text-white transition-colors"
              >
                {breadcrumb.icon && (
                  <breadcrumb.icon className="w-4 h-4 ml-2" />
                )}
                {breadcrumb.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

export default Breadcrumb
