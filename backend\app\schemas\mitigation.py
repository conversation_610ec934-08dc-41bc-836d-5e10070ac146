"""
مخططات بيانات خطط التخفيف
"""
from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime
from ..models.mitigation import MitigationStatus


class MitigationActionBase(BaseModel):
    """المخطط الأساسي لإجراءات التخفيف"""
    title: str
    description: Optional[str] = None
    priority: int = 2  # 1 = عالي، 2 = متوسط، 3 = منخفض
    responsible_id: int
    start_date: Optional[datetime] = None
    due_date: datetime
    is_critical: bool = False
    estimated_cost: Optional[float] = None
    resources_required: Optional[str] = None
    notes: Optional[str] = None

    @validator('title')
    def title_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('عنوان الإجراء مطلوب')
        if len(v.strip()) < 3:
            raise ValueError('عنوان الإجراء يجب أن يكون 3 أحرف على الأقل')
        if len(v.strip()) > 200:
            raise ValueError('عنوان الإجراء يجب أن يكون أقل من 200 حرف')
        return v.strip()

    @validator('priority')
    def priority_validation(cls, v):
        if v not in [1, 2, 3]:
            raise ValueError('الأولوية يجب أن تكون 1 (عالي) أو 2 (متوسط) أو 3 (منخفض)')
        return v

    @validator('estimated_cost')
    def cost_validation(cls, v):
        if v is not None and v < 0:
            raise ValueError('التكلفة المقدرة يجب أن تكون أكبر من أو تساوي صفر')
        return v


class MitigationActionCreate(MitigationActionBase):
    """مخطط إنشاء إجراء تخفيف جديد"""
    mitigation_plan_id: int


class MitigationActionUpdate(BaseModel):
    """مخطط تحديث إجراء التخفيف"""
    title: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[int] = None
    responsible_id: Optional[int] = None
    start_date: Optional[datetime] = None
    due_date: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    status: Optional[MitigationStatus] = None
    progress_percentage: Optional[float] = None
    is_critical: Optional[bool] = None
    estimated_cost: Optional[float] = None
    actual_cost: Optional[float] = None
    resources_required: Optional[str] = None
    notes: Optional[str] = None
    completion_notes: Optional[str] = None

    @validator('progress_percentage')
    def progress_validation(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError('نسبة الإنجاز يجب أن تكون بين 0 و 100')
        return v


class MitigationActionInDB(MitigationActionBase):
    """مخطط إجراء التخفيف في قاعدة البيانات"""
    id: int
    mitigation_plan_id: int
    completion_date: Optional[datetime] = None
    status: MitigationStatus
    progress_percentage: float = 0.0
    actual_cost: Optional[float] = None
    completion_notes: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class MitigationAction(MitigationActionInDB):
    """مخطط إجراء التخفيف للعرض"""
    responsible_name: Optional[str] = None
    is_overdue: bool = False
    days_remaining: int = 0


class MitigationPlanBase(BaseModel):
    """المخطط الأساسي لخطة التخفيف"""
    title: str
    description: Optional[str] = None
    objective: str
    strategy: str  # تجنب، تقليل، نقل، قبول
    approach: Optional[str] = None
    manager_id: Optional[int] = None
    start_date: Optional[datetime] = None
    target_completion_date: datetime
    expected_probability_reduction: Optional[str] = None
    expected_impact_reduction: Optional[str] = None
    expected_risk_level: Optional[str] = None
    total_estimated_cost: Optional[float] = None
    review_frequency: int = 30
    notes: Optional[str] = None

    @validator('title')
    def title_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('عنوان خطة التخفيف مطلوب')
        if len(v.strip()) < 5:
            raise ValueError('عنوان خطة التخفيف يجب أن يكون 5 أحرف على الأقل')
        if len(v.strip()) > 200:
            raise ValueError('عنوان خطة التخفيف يجب أن يكون أقل من 200 حرف')
        return v.strip()

    @validator('objective')
    def objective_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('هدف خطة التخفيف مطلوب')
        if len(v.strip()) < 10:
            raise ValueError('هدف خطة التخفيف يجب أن يكون 10 أحرف على الأقل')
        return v.strip()

    @validator('strategy')
    def strategy_validation(cls, v):
        valid_strategies = ['تجنب', 'تقليل', 'نقل', 'قبول', 'avoid', 'reduce', 'transfer', 'accept']
        if v not in valid_strategies:
            raise ValueError('استراتيجية التخفيف يجب أن تكون: تجنب، تقليل، نقل، أو قبول')
        return v

    @validator('review_frequency')
    def review_frequency_validation(cls, v):
        if v < 1 or v > 365:
            raise ValueError('تكرار المراجعة يجب أن يكون بين 1 و 365 يوم')
        return v


class MitigationPlanCreate(MitigationPlanBase):
    """مخطط إنشاء خطة تخفيف جديدة"""
    risk_id: int
    owner_id: int


class MitigationPlanUpdate(BaseModel):
    """مخطط تحديث خطة التخفيف"""
    title: Optional[str] = None
    description: Optional[str] = None
    objective: Optional[str] = None
    strategy: Optional[str] = None
    approach: Optional[str] = None
    manager_id: Optional[int] = None
    start_date: Optional[datetime] = None
    target_completion_date: Optional[datetime] = None
    actual_completion_date: Optional[datetime] = None
    status: Optional[MitigationStatus] = None
    expected_probability_reduction: Optional[str] = None
    expected_impact_reduction: Optional[str] = None
    expected_risk_level: Optional[str] = None
    total_estimated_cost: Optional[float] = None
    total_actual_cost: Optional[float] = None
    budget_approved: Optional[bool] = None
    review_frequency: Optional[int] = None
    notes: Optional[str] = None
    lessons_learned: Optional[str] = None


class MitigationPlanInDB(MitigationPlanBase):
    """مخطط خطة التخفيف في قاعدة البيانات"""
    id: int
    risk_id: int
    owner_id: int
    actual_completion_date: Optional[datetime] = None
    status: MitigationStatus
    overall_progress: float = 0.0
    total_actual_cost: Optional[float] = None
    budget_approved: bool = False
    approved_by_id: Optional[int] = None
    approval_date: Optional[datetime] = None
    last_review_date: Optional[datetime] = None
    next_review_date: Optional[datetime] = None
    lessons_learned: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class MitigationPlan(MitigationPlanInDB):
    """مخطط خطة التخفيف للعرض"""
    risk_title: Optional[str] = None
    owner_name: Optional[str] = None
    manager_name: Optional[str] = None
    approved_by_name: Optional[str] = None
    actions_count: int = 0
    completed_actions: int = 0
    overdue_actions: int = 0
    critical_actions: int = 0
    is_overdue: bool = False


class MitigationPlanWithActions(MitigationPlan):
    """مخطط خطة التخفيف مع الإجراءات"""
    actions: List[MitigationAction] = []


class MitigationPlanListResponse(BaseModel):
    """مخطط قائمة خطط التخفيف مع التصفح"""
    data: List[MitigationPlan]
    total: int
    page: int = 1
    per_page: int = 10
    total_pages: int

    @validator('total_pages', always=True)
    def calculate_total_pages(cls, v, values):
        total = values.get('total', 0)
        per_page = values.get('per_page', 10)
        return (total + per_page - 1) // per_page if per_page > 0 else 0


class MitigationStatistics(BaseModel):
    """إحصائيات خطط التخفيف"""
    total_plans: int = 0
    by_status: dict = {}
    by_strategy: dict = {}
    overdue_plans: int = 0
    completed_plans: int = 0
    total_actions: int = 0
    overdue_actions: int = 0
    critical_actions: int = 0
    average_progress: float = 0.0
