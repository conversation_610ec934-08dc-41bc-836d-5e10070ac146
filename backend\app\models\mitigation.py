"""
نموذج خطط التخفيف
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, Float, Foreign<PERSON><PERSON>, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..database import Base


class MitigationStatus(str, enum.Enum):
    """حالات خطط التخفيف"""
    PLANNED = "PLANNED"                   # مخطط
    IN_PROGRESS = "IN_PROGRESS"           # قيد التنفيذ
    COMPLETED = "COMPLETED"               # مكتمل
    ON_HOLD = "ON_HOLD"                   # معلق
    CANCELLED = "CANCELLED"               # ملغي
    OVERDUE = "OVERDUE"                   # متأخر


class MitigationAction(Base):
    """نموذج إجراءات التخفيف"""
    __tablename__ = "mitigation_actions"
    
    id = Column(Integer, primary_key=True, index=True)
    mitigation_plan_id = Column(Integer, ForeignKey("mitigation_plans.id"), nullable=False)
    
    # تفاصيل الإجراء
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    priority = Column(Integer, default=1)  # 1 = عالي، 2 = متوسط، 3 = منخفض
    
    # المسؤوليات والمواعيد
    responsible_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    start_date = Column(DateTime(timezone=True), nullable=True)
    due_date = Column(DateTime(timezone=True), nullable=False)
    completion_date = Column(DateTime(timezone=True), nullable=True)
    
    # الحالة والتقدم
    status = Column(Enum(MitigationStatus), default=MitigationStatus.PLANNED)
    progress_percentage = Column(Float, default=0.0)  # نسبة الإنجاز
    is_critical = Column(Boolean, default=False)      # إجراء حرج
    
    # التكلفة والموارد
    estimated_cost = Column(Float, nullable=True)
    actual_cost = Column(Float, nullable=True)
    resources_required = Column(Text, nullable=True)
    
    # ملاحظات ومتابعة
    notes = Column(Text, nullable=True)
    completion_notes = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    mitigation_plan = relationship("MitigationPlan", back_populates="actions")
    responsible = relationship("User", back_populates="mitigation_actions")
    
    @property
    def is_overdue(self):
        """التحقق من تأخر الإجراء"""
        from datetime import datetime, timezone
        if self.status in [MitigationStatus.COMPLETED, MitigationStatus.CANCELLED]:
            return False
        return self.due_date < datetime.now(timezone.utc)
    
    @property
    def days_remaining(self):
        """عدد الأيام المتبقية"""
        from datetime import datetime, timezone
        if self.status in [MitigationStatus.COMPLETED, MitigationStatus.CANCELLED]:
            return 0
        delta = self.due_date - datetime.now(timezone.utc)
        return max(0, delta.days)


class MitigationPlan(Base):
    """نموذج خطة التخفيف"""
    __tablename__ = "mitigation_plans"
    
    id = Column(Integer, primary_key=True, index=True)
    risk_id = Column(Integer, ForeignKey("risks.id"), nullable=False)
    
    # تفاصيل الخطة
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    objective = Column(Text, nullable=False)  # هدف الخطة
    
    # استراتيجية التخفيف
    strategy = Column(String(100), nullable=False)  # تجنب، تقليل، نقل، قبول
    approach = Column(Text, nullable=True)           # منهج التنفيذ
    
    # المسؤوليات
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    manager_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # الجدولة
    start_date = Column(DateTime(timezone=True), nullable=True)
    target_completion_date = Column(DateTime(timezone=True), nullable=False)
    actual_completion_date = Column(DateTime(timezone=True), nullable=True)
    
    # الحالة والتقدم
    status = Column(Enum(MitigationStatus), default=MitigationStatus.PLANNED)
    overall_progress = Column(Float, default=0.0)  # نسبة الإنجاز الإجمالية
    
    # التقييم المتوقع بعد التنفيذ
    expected_probability_reduction = Column(String(50), nullable=True)
    expected_impact_reduction = Column(String(50), nullable=True)
    expected_risk_level = Column(String(50), nullable=True)
    
    # التكلفة والموارد
    total_estimated_cost = Column(Float, nullable=True)
    total_actual_cost = Column(Float, nullable=True)
    budget_approved = Column(Boolean, default=False)
    
    # المراجعة والموافقة
    approved_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    approval_date = Column(DateTime(timezone=True), nullable=True)
    review_frequency = Column(Integer, default=30)  # مراجعة كل كم يوم
    last_review_date = Column(DateTime(timezone=True), nullable=True)
    next_review_date = Column(DateTime(timezone=True), nullable=True)
    
    # ملاحظات
    notes = Column(Text, nullable=True)
    lessons_learned = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    risk = relationship("Risk", back_populates="mitigation_plans")
    owner = relationship("User", foreign_keys=[owner_id], back_populates="owned_mitigation_plans")
    manager = relationship("User", foreign_keys=[manager_id], back_populates="managed_mitigation_plans")
    approved_by = relationship("User", foreign_keys=[approved_by_id])
    actions = relationship("MitigationAction", back_populates="mitigation_plan", cascade="all, delete-orphan")
    
    def calculate_overall_progress(self):
        """حساب نسبة الإنجاز الإجمالية"""
        if not self.actions:
            return 0.0
        
        total_progress = sum(action.progress_percentage for action in self.actions)
        self.overall_progress = total_progress / len(self.actions)
        return self.overall_progress
    
    @property
    def is_overdue(self):
        """التحقق من تأخر الخطة"""
        from datetime import datetime, timezone
        if self.status in [MitigationStatus.COMPLETED, MitigationStatus.CANCELLED]:
            return False
        return self.target_completion_date < datetime.now(timezone.utc)
    
    @property
    def critical_actions_count(self):
        """عدد الإجراءات الحرجة"""
        return len([action for action in self.actions if action.is_critical])
    
    @property
    def overdue_actions_count(self):
        """عدد الإجراءات المتأخرة"""
        return len([action for action in self.actions if action.is_overdue])
    
    @property
    def completed_actions_count(self):
        """عدد الإجراءات المكتملة"""
        return len([action for action in self.actions if action.status == MitigationStatus.COMPLETED])
