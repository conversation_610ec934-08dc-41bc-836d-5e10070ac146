"""
مخططات المستخدمين
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator
from ..models.user import UserRole


class UserBase(BaseModel):
    """المخطط الأساسي للمستخدم"""
    username: str
    email: str
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER
    is_active: bool = True

    @validator('username')
    def username_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('اسم المستخدم مطلوب')
        if len(v.strip()) < 3:
            raise ValueError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        if len(v.strip()) > 50:
            raise ValueError('اسم المستخدم يجب أن يكون أقل من 50 حرف')
        # التحقق من أن اسم المستخدم يحتوي على أحرف وأرقام فقط
        if not v.strip().replace('_', '').replace('-', '').isalnum():
            raise ValueError('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط')
        return v.strip().lower()

    @validator('email')
    def email_validation(cls, v):
        if v:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, v):
                raise ValueError('البريد الإلكتروني غير صحيح')
        return v

    @validator('full_name')
    def full_name_validation(cls, v):
        if v and len(v) > 100:
            raise ValueError('الاسم الكامل يجب أن يكون أقل من 100 حرف')
        return v


class UserCreate(UserBase):
    """مخطط إنشاء مستخدم"""
    password: str

    @validator('password')
    def password_validation(cls, v):
        if not v or len(v) < 8:
            raise ValueError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        if len(v) > 128:
            raise ValueError('كلمة المرور يجب أن تكون أقل من 128 حرف')

        # التحقق من وجود أحرف كبيرة وصغيرة وأرقام
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError('كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام')

        return v


class UserUpdate(BaseModel):
    """مخطط تحديث المستخدم"""
    username: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    password: Optional[str] = None

    @validator('username')
    def username_validation(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('اسم المستخدم لا يمكن أن يكون فارغاً')
            if len(v.strip()) < 3:
                raise ValueError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
            if len(v.strip()) > 50:
                raise ValueError('اسم المستخدم يجب أن يكون أقل من 50 حرف')
            if not v.strip().replace('_', '').replace('-', '').isalnum():
                raise ValueError('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط')
            return v.strip().lower()
        return v

    @validator('password')
    def password_validation(cls, v):
        if v is not None:
            if len(v) < 8:
                raise ValueError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
            if len(v) > 128:
                raise ValueError('كلمة المرور يجب أن تكون أقل من 128 حرف')

            # التحقق من وجود أحرف كبيرة وصغيرة وأرقام
            has_upper = any(c.isupper() for c in v)
            has_lower = any(c.islower() for c in v)
            has_digit = any(c.isdigit() for c in v)

            if not (has_upper and has_lower and has_digit):
                raise ValueError('كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام')

        return v

    @validator('full_name')
    def full_name_validation(cls, v):
        if v is not None and len(v) > 100:
            raise ValueError('الاسم الكامل يجب أن يكون أقل من 100 حرف')
        return v


class UserInDB(UserBase):
    """مخطط المستخدم في قاعدة البيانات"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class User(UserInDB):
    """مخطط المستخدم للعرض"""
    pass


class UserWithStats(User):
    """مخطط المستخدم مع الإحصائيات"""
    incidents_count: int = 0
    last_login: Optional[datetime] = None


class UserListResponse(BaseModel):
    """مخطط قائمة المستخدمين مع التصفح"""
    data: list[User]
    total: int
    page: int = 1
    per_page: int = 10
    total_pages: int

    @validator('total_pages', always=True)
    def calculate_total_pages(cls, v, values):
        total = values.get('total', 0)
        per_page = values.get('per_page', 10)
        return (total + per_page - 1) // per_page if per_page > 0 else 0


class PasswordChange(BaseModel):
    """مخطط تغيير كلمة المرور"""
    current_password: str
    new_password: str
    confirm_password: str

    @validator('new_password')
    def password_validation(cls, v):
        if len(v) < 8:
            raise ValueError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        if len(v) > 128:
            raise ValueError('كلمة المرور يجب أن تكون أقل من 128 حرف')

        # التحقق من وجود أحرف كبيرة وصغيرة وأرقام
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError('كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام')

        return v

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين')
        return v


class UserProfile(BaseModel):
    """مخطط الملف الشخصي للمستخدم"""
    username: str
    email: str
    full_name: Optional[str] = None
    role: UserRole
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True
