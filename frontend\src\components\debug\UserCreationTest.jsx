import React, { useState } from 'react'
import { usersService } from '../../services/users'

const UserCreationTest = () => {
  const [testResult, setTestResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const testUserCreation = async () => {
    setIsLoading(true)
    setTestResult('')

    try {
      // اختبار إنشاء مستخدم جديد
      const testUser = {
        username: `testuser_${Date.now()}`,
        email: `test_${Date.now()}@example.com`,
        full_name: 'Test User',
        password: 'TestPass123!',
        role: 'USER',
        is_active: true
      }

      console.log('Testing user creation with data:', testUser)
      
      const result = await usersService.createUser(testUser)
      
      console.log('User creation result:', result)
      
      setTestResult(`✅ تم إنشاء المستخدم بنجاح: ${result.username} (ID: ${result.id})`)
      
    } catch (error) {
      console.error('User creation test failed:', error)
      setTestResult(`❌ فشل في إنشاء المستخدم: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testServiceConnection = async () => {
    setIsLoading(true)
    setTestResult('')

    try {
      // اختبار الاتصال بخدمة المستخدمين
      const users = await usersService.getUsers()
      setTestResult(`✅ تم الاتصال بخدمة المستخدمين بنجاح. عدد المستخدمين: ${users.length}`)
    } catch (error) {
      console.error('Service connection test failed:', error)
      setTestResult(`❌ فشل في الاتصال بخدمة المستخدمين: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 m-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        اختبار إنشاء المستخدمين
      </h3>
      
      <div className="space-y-4">
        <div className="flex space-x-4 space-x-reverse">
          <button
            onClick={testServiceConnection}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'جاري الاختبار...' : 'اختبار الاتصال'}
          </button>
          
          <button
            onClick={testUserCreation}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            {isLoading ? 'جاري الاختبار...' : 'اختبار إنشاء مستخدم'}
          </button>
        </div>
        
        {testResult && (
          <div className={`p-4 rounded-lg ${
            testResult.includes('✅') 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
          }`}>
            {testResult}
          </div>
        )}
      </div>
    </div>
  )
}

export default UserCreationTest
