// ثوابت التطبيق

// عنوان API
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

// أنواع الحوادث السيبرانية
export const INCIDENT_TYPES = {
  malware: 'برمجيات خبيثة',
  phishing: 'تصيد إلكتروني',
  data_breach: 'تسريب بيانات',
  unauthorized_access: 'وصول غير مصرح',
  denial_of_service: 'حرمان من الخدمة',
  insider_threat: 'تهديد داخلي',
  ransomware: 'برمجيات الفدية',
  social_engineering: 'هندسة اجتماعية',
  other: 'أخرى'
}

// مستويات الخطورة
export const SEVERITY_LEVELS = {
  low: 'منخفض',
  medium: 'متوسط',
  high: 'عالي',
  critical: 'حرج'
}

// حالات الحوادث
export const INCIDENT_STATUS = {
  open: 'مفتوح',
  in_progress: 'قيد المعالجة',
  resolved: 'محلول',
  closed: 'مغلق'
}

// ألوان مستويات الخطورة
export const SEVERITY_COLORS = {
  low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
}

// ألوان حالات الحوادث
export const STATUS_COLORS = {
  open: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  in_progress: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  resolved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  closed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

// أدوار المستخدمين
export const USER_ROLES = {
  ADMIN: 'مدير النظام',
  RISK_MANAGER: 'مدير المخاطر',
  COMPLIANCE_OFFICER: 'مسؤول الامتثال',
  AUDITOR: 'مدقق',
  USER: 'مستخدم'
}

// أنواع المخاطر
export const RISK_TYPES = {
  OPERATIONAL: 'تشغيلية',
  FINANCIAL: 'مالية',
  STRATEGIC: 'استراتيجية',
  COMPLIANCE: 'امتثال',
  REPUTATIONAL: 'سمعة',
  TECHNOLOGY: 'تقنية',
  CYBERSECURITY: 'أمن سيبراني',
  LEGAL: 'قانونية'
}

// مستويات المخاطر
export const RISK_LEVELS = {
  VERY_LOW: 'منخفض جداً',
  LOW: 'منخفض',
  MEDIUM: 'متوسط',
  HIGH: 'عالي',
  VERY_HIGH: 'عالي جداً'
}

// حالات المخاطر
export const RISK_STATUS = {
  IDENTIFIED: 'محدد',
  ASSESSED: 'مقيم',
  MITIGATED: 'مخفف',
  MONITORED: 'مراقب',
  CLOSED: 'مغلق'
}

// أنواع السياسات
export const POLICY_TYPES = {
  SECURITY: 'أمنية',
  PRIVACY: 'خصوصية',
  OPERATIONAL: 'تشغيلية',
  HR: 'موارد بشرية',
  FINANCIAL: 'مالية',
  IT: 'تقنية معلومات'
}

// حالات السياسات
export const POLICY_STATUS = {
  DRAFT: 'مسودة',
  REVIEW: 'قيد المراجعة',
  APPROVED: 'معتمدة',
  ACTIVE: 'نشطة',
  ARCHIVED: 'مؤرشفة'
}

// معايير الامتثال
export const COMPLIANCE_STANDARDS = {
  ISO_27001: 'ISO 27001',
  NCA_ECC: 'NCA ECC',
  GDPR: 'GDPR',
  SOX: 'SOX',
  PCI_DSS: 'PCI DSS',
  NIST: 'NIST Framework'
}

// رسائل التحقق
export const VALIDATION_MESSAGES = {
  required: 'هذا الحقل مطلوب',
  email: 'يرجى إدخال بريد إلكتروني صالح',
  minLength: (min) => `يجب أن يكون الطول ${min} أحرف على الأقل`,
  maxLength: (max) => `يجب أن لا يتجاوز الطول ${max} حرف`,
  passwordStrength: 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام ورموز خاصة'
}

// إعدادات التطبيق
export const APP_CONFIG = {
  name: 'نظام إدارة الحوكمة والمخاطر والامتثال',
  version: '1.0.0',
  itemsPerPage: 10,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  supportedFileTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png']
}

// مسارات التطبيق
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',

  // إدارة الحوادث
  INCIDENTS: '/incidents',
  INCIDENT_CREATE: '/incidents/create',
  INCIDENT_EDIT: '/incidents/edit',

  // إدارة المستخدمين
  USERS: '/users',
  USER_CREATE: '/users/create',
  USER_EDIT: '/users/edit',

  // إدارة المخاطر
  RISKS: '/risks',
  RISK_CREATE: '/risks/create',
  RISK_EDIT: '/risks/edit',
  RISK_MATRIX: '/risks/matrix',
  RISK_ASSESSMENT: '/risks/assessment',

  // الحوكمة والامتثال
  GOVERNANCE: '/governance',
  POLICIES: '/governance/policies',
  POLICY_CREATE: '/governance/policies/create',
  POLICY_EDIT: '/governance/policies/edit',
  COMPLIANCE: '/governance/compliance',
  COMPLIANCE_CREATE: '/governance/compliance/create',
  AUDITS: '/governance/audits',
  AUDIT_CREATE: '/governance/audits/create',

  // التقارير
  REPORTS: '/reports',
  REPORTS_INCIDENTS: '/reports/incidents',
  REPORTS_RISKS: '/reports/risks',
  REPORTS_COMPLIANCE: '/reports/compliance',
  REPORTS_DASHBOARD: '/reports/dashboard',

  // الإعدادات والملف الشخصي
  PROFILE: '/profile',
  SETTINGS: '/settings',
  SYSTEM_SETTINGS: '/settings/system'
}
