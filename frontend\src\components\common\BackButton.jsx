import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowRightIcon } from '@heroicons/react/24/outline'

const BackButton = ({ 
  to = null, 
  label = 'العودة', 
  className = '',
  variant = 'secondary' 
}) => {
  const navigate = useNavigate()

  const handleClick = () => {
    if (to) {
      navigate(to)
    } else {
      navigate(-1) // العودة للصفحة السابقة
    }
  }

  const baseClasses = "inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-900"
  
  const variants = {
    primary: "bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",
    secondary: "bg-dark-700 text-dark-300 hover:bg-dark-600 hover:text-white focus:ring-dark-500 border border-dark-600",
    ghost: "text-dark-300 hover:text-white hover:bg-dark-700 focus:ring-dark-500"
  }

  const classes = `${baseClasses} ${variants[variant]} ${className}`

  return (
    <button
      onClick={handleClick}
      className={classes}
      type="button"
    >
      <ArrowRightIcon className="w-4 h-4 ml-2" />
      {label}
    </button>
  )
}

export default BackButton
