"""
نموذج المستخدمين
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..database import Base


class UserRole(str, enum.Enum):
    """أدوار المستخدمين"""
    ADMIN = "ADMIN"                    # مدير النظام
    RISK_MANAGER = "RISK_MANAGER"      # مدير المخاطر
    COMPLIANCE_OFFICER = "COMPLIANCE_OFFICER"  # مسؤول الامتثال
    AUDITOR = "AUDITOR"                # مدقق
    USER = "USER"                      # مستخدم عادي


class User(Base):
    """نموذج المستخدم"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    incidents = relationship("Incident", back_populates="created_by_user")
    logs = relationship("Log", back_populates="user")

    # علاقات المخاطر
    owned_risks = relationship("Risk", foreign_keys="Risk.owner_id", back_populates="owner")
    responsible_risks = relationship("Risk", foreign_keys="Risk.responsible_id", back_populates="responsible")

    # علاقات خطط التخفيف
    owned_mitigation_plans = relationship("MitigationPlan", foreign_keys="MitigationPlan.owner_id", back_populates="owner")
    managed_mitigation_plans = relationship("MitigationPlan", foreign_keys="MitigationPlan.manager_id", back_populates="manager")
    mitigation_actions = relationship("MitigationAction", back_populates="responsible")
    
    def __repr__(self):
        return f"<User(username='{self.username}', role='{self.role}')>"
