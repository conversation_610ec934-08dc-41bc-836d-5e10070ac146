"""
مخططات بيانات المخاطر
"""
from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime
from ..models.risk import RiskType, RiskLevel, RiskStatus, RiskProbability, RiskImpact


class RiskBase(BaseModel):
    """المخطط الأساسي للمخاطر"""
    title: str
    description: Optional[str] = None
    risk_type: RiskType
    probability: RiskProbability
    impact: RiskImpact
    category: Optional[str] = None
    source: Optional[str] = None
    triggers: Optional[str] = None
    responsible_id: Optional[int] = None
    next_review_date: Optional[datetime] = None

    @validator('title')
    def title_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('عنوان المخاطرة مطلوب')
        if len(v.strip()) < 5:
            raise ValueError('عنوان المخاطرة يجب أن يكون 5 أحرف على الأقل')
        if len(v.strip()) > 200:
            raise ValueError('عنوان المخاطرة يجب أن يكون أقل من 200 حرف')
        return v.strip()

    @validator('description')
    def description_validation(cls, v):
        if v and len(v.strip()) > 2000:
            raise ValueError('وصف المخاطرة يجب أن يكون أقل من 2000 حرف')
        return v.strip() if v else None

    @validator('category')
    def category_validation(cls, v):
        if v and len(v.strip()) > 100:
            raise ValueError('فئة المخاطرة يجب أن تكون أقل من 100 حرف')
        return v.strip() if v else None


class RiskCreate(RiskBase):
    """مخطط إنشاء مخاطرة جديدة"""
    owner_id: int


class RiskUpdate(BaseModel):
    """مخطط تحديث المخاطرة"""
    title: Optional[str] = None
    description: Optional[str] = None
    risk_type: Optional[RiskType] = None
    probability: Optional[RiskProbability] = None
    impact: Optional[RiskImpact] = None
    category: Optional[str] = None
    source: Optional[str] = None
    triggers: Optional[str] = None
    responsible_id: Optional[int] = None
    status: Optional[RiskStatus] = None
    next_review_date: Optional[datetime] = None

    @validator('title')
    def title_validation(cls, v):
        if v is not None:
            if not v or not v.strip():
                raise ValueError('عنوان المخاطرة مطلوب')
            if len(v.strip()) < 5:
                raise ValueError('عنوان المخاطرة يجب أن يكون 5 أحرف على الأقل')
            if len(v.strip()) > 200:
                raise ValueError('عنوان المخاطرة يجب أن يكون أقل من 200 حرف')
            return v.strip()
        return v


class RiskAssessment(BaseModel):
    """مخطط تقييم المخاطرة"""
    probability: RiskProbability
    impact: RiskImpact
    assessment_notes: Optional[str] = None


class RiskMatrixPosition(BaseModel):
    """موقع المخاطرة في المصفوفة"""
    probability: int
    impact: int
    score: float
    level: str


class RiskInDB(RiskBase):
    """مخطط المخاطرة في قاعدة البيانات"""
    id: int
    owner_id: int
    risk_score: Optional[float] = None
    risk_level: Optional[RiskLevel] = None
    status: RiskStatus
    identified_date: datetime
    last_assessment_date: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class Risk(RiskInDB):
    """مخطط المخاطرة للعرض"""
    matrix_position: Optional[RiskMatrixPosition] = None


class RiskWithDetails(Risk):
    """مخطط المخاطرة مع التفاصيل"""
    owner_name: Optional[str] = None
    responsible_name: Optional[str] = None
    mitigation_plans_count: int = 0
    active_mitigation_plans: int = 0
    overdue_actions: int = 0


class RiskListResponse(BaseModel):
    """مخطط قائمة المخاطر مع التصفح"""
    data: List[RiskWithDetails]
    total: int
    page: int = 1
    per_page: int = 10
    total_pages: int

    @validator('total_pages', always=True)
    def calculate_total_pages(cls, v, values):
        total = values.get('total', 0)
        per_page = values.get('per_page', 10)
        return (total + per_page - 1) // per_page if per_page > 0 else 0


class RiskStatistics(BaseModel):
    """إحصائيات المخاطر"""
    total_risks: int = 0
    by_level: dict = {}
    by_type: dict = {}
    by_status: dict = {}
    high_priority_risks: int = 0
    overdue_reviews: int = 0
    recent_assessments: int = 0


class RiskMatrixData(BaseModel):
    """بيانات مصفوفة المخاطر"""
    risks: List[dict]
    matrix_config: dict
    statistics: RiskStatistics


class RiskFilter(BaseModel):
    """فلتر البحث في المخاطر"""
    search: Optional[str] = None
    risk_type: Optional[RiskType] = None
    risk_level: Optional[RiskLevel] = None
    status: Optional[RiskStatus] = None
    owner_id: Optional[int] = None
    responsible_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    overdue_only: Optional[bool] = False
    high_priority_only: Optional[bool] = False
