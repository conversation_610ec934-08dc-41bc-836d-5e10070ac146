import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { 
  MagnifyingGlassIcon, 
  PlusIcon, 
  EyeIcon, 
  PencilIcon, 
  TrashIcon,
  FunnelIcon,
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { getRisks, deleteRisk, formatRiskLevel, formatRiskType, formatRiskStatus, getRiskLevelColor } from '../services/risks'
import { RISK_TYPES, RISK_LEVELS, RISK_STATUS } from '../utils/constants'

const RiskList = () => {
  const navigate = useNavigate()
  const [risks, setRisks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalRisks, setTotalRisks] = useState(0)
  
  // فلاتر البحث
  const [filters, setFilters] = useState({
    search: '',
    risk_type: '',
    risk_level: '',
    status: '',
    overdue_only: false,
    high_priority_only: false
  })
  const [showFilters, setShowFilters] = useState(false)

  // جلب البيانات
  const fetchRisks = async (page = 1) => {
    try {
      setLoading(true)
      const params = {
        page,
        per_page: 10,
        ...filters
      }
      
      // إزالة الفلاتر الفارغة
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === false) {
          delete params[key]
        }
      })

      const response = await getRisks(params)
      setRisks(response.data)
      setCurrentPage(response.page)
      setTotalPages(response.total_pages)
      setTotalRisks(response.total)
    } catch (err) {
      setError('فشل في جلب المخاطر')
      console.error('Error fetching risks:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRisks(1)
  }, [filters])

  // معالجة البحث
  const handleSearch = (e) => {
    setFilters(prev => ({ ...prev, search: e.target.value }))
    setCurrentPage(1)
  }

  // معالجة الفلاتر
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  // مسح الفلاتر
  const clearFilters = () => {
    setFilters({
      search: '',
      risk_type: '',
      risk_level: '',
      status: '',
      overdue_only: false,
      high_priority_only: false
    })
    setCurrentPage(1)
  }

  // حذف مخاطرة
  const handleDelete = async (riskId, riskTitle) => {
    if (window.confirm(`هل أنت متأكد من حذف المخاطرة "${riskTitle}"؟`)) {
      try {
        await deleteRisk(riskId)
        fetchRisks(currentPage)
      } catch (err) {
        setError('فشل في حذف المخاطرة')
        console.error('Error deleting risk:', err)
      }
    }
  }

  // تغيير الصفحة
  const handlePageChange = (page) => {
    setCurrentPage(page)
    fetchRisks(page)
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان والإجراءات */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">إدارة المخاطر</h1>
          <p className="text-gray-400 mt-1">
            عرض وإدارة جميع المخاطر ({totalRisks} مخاطرة)
          </p>
        </div>
        <Link
          to="/risks/new"
          className="btn-primary flex items-center gap-2"
        >
          <PlusIcon className="h-5 w-5" />
          إضافة مخاطرة جديدة
        </Link>
      </div>

      {/* شريط البحث والفلاتر */}
      <div className="bg-dark-800 rounded-lg p-4 space-y-4">
        <div className="flex gap-4">
          {/* البحث */}
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في المخاطر..."
              value={filters.search}
              onChange={handleSearch}
              className="input-field pl-10"
            />
          </div>
          
          {/* زر الفلاتر */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn-secondary flex items-center gap-2 ${showFilters ? 'bg-primary-600 text-white' : ''}`}
          >
            <FunnelIcon className="h-5 w-5" />
            فلاتر
          </button>
        </div>

        {/* الفلاتر المتقدمة */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 pt-4 border-t border-dark-700">
            {/* نوع المخاطرة */}
            <select
              value={filters.risk_type}
              onChange={(e) => handleFilterChange('risk_type', e.target.value)}
              className="input-field"
            >
              <option value="">جميع الأنواع</option>
              {Object.entries(RISK_TYPES).map(([key, value]) => (
                <option key={key} value={key}>{value}</option>
              ))}
            </select>

            {/* مستوى المخاطرة */}
            <select
              value={filters.risk_level}
              onChange={(e) => handleFilterChange('risk_level', e.target.value)}
              className="input-field"
            >
              <option value="">جميع المستويات</option>
              {Object.entries(RISK_LEVELS).map(([key, value]) => (
                <option key={key} value={key}>{value}</option>
              ))}
            </select>

            {/* الحالة */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="input-field"
            >
              <option value="">جميع الحالات</option>
              {Object.entries(RISK_STATUS).map(([key, value]) => (
                <option key={key} value={key}>{value}</option>
              ))}
            </select>

            {/* فلاتر سريعة */}
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={filters.high_priority_only}
                onChange={(e) => handleFilterChange('high_priority_only', e.target.checked)}
                className="rounded border-gray-600 bg-dark-700 text-primary-600"
              />
              عالية الأولوية فقط
            </label>

            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={filters.overdue_only}
                onChange={(e) => handleFilterChange('overdue_only', e.target.checked)}
                className="rounded border-gray-600 bg-dark-700 text-primary-600"
              />
              متأخرة المراجعة فقط
            </label>

            {/* مسح الفلاتر */}
            <button
              onClick={clearFilters}
              className="btn-secondary text-sm"
            >
              مسح الفلاتر
            </button>
          </div>
        )}
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* جدول المخاطر */}
      <div className="bg-dark-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-dark-700">
            <thead className="bg-dark-900">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  المخاطرة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  النوع
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  المستوى
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  المالك
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  خطط التخفيف
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-dark-800 divide-y divide-dark-700">
              {risks.map((risk) => (
                <tr key={risk.id} className="hover:bg-dark-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-white">
                        {risk.title}
                      </div>
                      {risk.description && (
                        <div className="text-sm text-gray-400 truncate max-w-xs">
                          {risk.description}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-300">
                      {formatRiskType(risk.risk_type)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(risk.risk_level)}`}>
                      {formatRiskLevel(risk.risk_level)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-300">
                      {formatRiskStatus(risk.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-300">
                      {risk.owner_name || 'غير محدد'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-300">
                        {risk.active_mitigation_plans}/{risk.mitigation_plans_count}
                      </span>
                      {risk.overdue_actions > 0 && (
                        <ExclamationTriangleIcon className="h-4 w-4 text-red-500" title="إجراءات متأخرة" />
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => navigate(`/risks/${risk.id}`)}
                        className="text-blue-400 hover:text-blue-300"
                        title="عرض التفاصيل"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => navigate(`/risks/${risk.id}/edit`)}
                        className="text-yellow-400 hover:text-yellow-300"
                        title="تعديل"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(risk.id, risk.title)}
                        className="text-red-400 hover:text-red-300"
                        title="حذف"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* رسالة عدم وجود بيانات */}
        {risks.length === 0 && !loading && (
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-300">لا توجد مخاطر</h3>
            <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة مخاطرة جديدة</p>
          </div>
        )}
      </div>

      {/* التصفح */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            عرض {((currentPage - 1) * 10) + 1} إلى {Math.min(currentPage * 10, totalRisks)} من {totalRisks} مخاطرة
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              السابق
            </button>
            
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  page === currentPage
                    ? 'bg-primary-600 text-white'
                    : 'bg-dark-700 text-gray-300 hover:bg-dark-600'
                }`}
              >
                {page}
              </button>
            ))}
            
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              التالي
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default RiskList
