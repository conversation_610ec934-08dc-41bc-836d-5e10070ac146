"""
مخططات الحوادث السيبرانية
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, validator
from ..models.incident import IncidentType, IncidentSeverity, IncidentStatus


class IncidentBase(BaseModel):
    """المخطط الأساسي للحادث"""
    title: str
    description: Optional[str] = None
    incident_type: IncidentType
    severity: IncidentSeverity = IncidentSeverity.MEDIUM
    incident_date: datetime
    affected_systems: Optional[str] = None
    impact_description: Optional[str] = None
    actions_taken: Optional[str] = None
    nca_reported: str = "لا"


class IncidentCreate(IncidentBase):
    """مخطط إنشاء حادث"""
    
    @validator('title')
    def title_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('عنوان الحادث مطلوب')
        if len(v.strip()) < 5:
            raise ValueError('عنوان الحادث يجب أن يكون 5 أحرف على الأقل')
        return v.strip()
    
    @validator('incident_date')
    def incident_date_validation(cls, v):
        # تحويل التاريخ إلى naive datetime للمقارنة
        if hasattr(v, 'replace') and hasattr(v, 'tzinfo') and v.tzinfo is not None:
            v_naive = v.replace(tzinfo=None)
        elif hasattr(v, 'date'):
            # إذا كان date object
            v_naive = datetime.combine(v, datetime.min.time())
        else:
            v_naive = v

        if isinstance(v_naive, datetime) and v_naive > datetime.now():
            raise ValueError('تاريخ الحادث لا يمكن أن يكون في المستقبل')
        return v


class IncidentUpdate(BaseModel):
    """مخطط تحديث الحادث"""
    title: Optional[str] = None
    description: Optional[str] = None
    incident_type: Optional[IncidentType] = None
    severity: Optional[IncidentSeverity] = None
    status: Optional[IncidentStatus] = None
    affected_systems: Optional[str] = None
    impact_description: Optional[str] = None
    actions_taken: Optional[str] = None
    lessons_learned: Optional[str] = None
    nca_reported: Optional[str] = None
    resolved_date: Optional[datetime] = None


class IncidentInDB(IncidentBase):
    """مخطط الحادث في قاعدة البيانات"""
    id: int
    status: IncidentStatus
    reference_number: Optional[str] = None
    lessons_learned: Optional[str] = None
    resolved_date: Optional[datetime] = None
    reported_date: datetime
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class Incident(IncidentInDB):
    """مخطط الحادث للعرض"""
    pass


class IncidentWithUser(Incident):
    """مخطط الحادث مع معلومات المستخدم"""
    created_by_username: str
    created_by_full_name: Optional[str] = None


class IncidentSummary(BaseModel):
    """ملخص الحادث"""
    id: int
    title: str
    incident_type: IncidentType
    severity: IncidentSeverity
    status: IncidentStatus
    incident_date: datetime
    created_by_username: str
    
    class Config:
        from_attributes = True


class IncidentStats(BaseModel):
    """إحصائيات الحوادث"""
    total_incidents: int = 0
    open_incidents: int = 0
    resolved_incidents: int = 0
    critical_incidents: int = 0
    incidents_by_type: dict = {}
    incidents_by_month: dict = {}
