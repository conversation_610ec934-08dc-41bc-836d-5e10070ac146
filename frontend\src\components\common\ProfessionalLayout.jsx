import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  HomeIcon,
  ExclamationTriangleIcon,
  UsersIcon,
  ShieldExclamationIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CogIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BellIcon,
  MagnifyingGlassIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline'
import { ROUTES } from '../../utils/constants'
import { PERMISSIONS, hasPermission } from '../../utils/permissions'
import Breadcrumb from './Breadcrumb'

const ProfessionalLayout = ({ children, user, onLogout }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState({})
  const location = useLocation()

  const toggleMenu = (menuKey) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }))
  }

  const navigation = [
    {
      name: 'لوحة التحكم',
      href: ROUTES.DASHBOARD,
      icon: HomeIcon,
      type: 'single',
      description: 'نظرة عامة على النظام',
      permission: PERMISSIONS.DASHBOARD_VIEW
    },
    {
      name: 'إدارة الحوادث',
      icon: ExclamationTriangleIcon,
      type: 'group',
      key: 'incidents',
      description: 'إدارة الحوادث السيبرانية',
      permission: PERMISSIONS.INCIDENTS_VIEW,
      children: [
        {
          name: 'جميع الحوادث',
          href: ROUTES.INCIDENTS,
          description: 'عرض وإدارة جميع الحوادث',
          permission: PERMISSIONS.INCIDENTS_VIEW
        },
        {
          name: 'إضافة حادث جديد',
          href: ROUTES.INCIDENT_CREATE,
          description: 'تسجيل حادث سيبراني جديد',
          permission: PERMISSIONS.INCIDENTS_CREATE
        }
      ]
    },
    {
      name: 'إدارة المخاطر',
      icon: ShieldExclamationIcon,
      type: 'group',
      key: 'risks',
      description: 'تحديد وتقييم وإدارة المخاطر',
      permission: PERMISSIONS.RISKS_VIEW,
      children: [
        {
          name: 'جميع المخاطر',
          href: ROUTES.RISKS,
          description: 'عرض وإدارة جميع المخاطر',
          permission: PERMISSIONS.RISKS_VIEW
        },
        {
          name: 'إضافة مخاطرة',
          href: ROUTES.RISK_CREATE,
          description: 'تسجيل مخاطرة جديدة',
          permission: PERMISSIONS.RISKS_CREATE
        },
        {
          name: 'مصفوفة المخاطر',
          href: ROUTES.RISK_MATRIX,
          description: 'عرض مصفوفة تقييم المخاطر',
          permission: PERMISSIONS.RISKS_VIEW
        }
      ]
    },
    {
      name: 'الحوكمة والامتثال',
      icon: DocumentTextIcon,
      type: 'group',
      key: 'governance',
      description: 'إدارة السياسات والامتثال',
      permission: PERMISSIONS.GOVERNANCE_VIEW,
      children: [
        {
          name: 'نظرة عامة',
          href: ROUTES.GOVERNANCE,
          description: 'لوحة تحكم الحوكمة',
          permission: PERMISSIONS.GOVERNANCE_VIEW
        },
        {
          name: 'السياسات والإجراءات',
          href: ROUTES.POLICIES,
          description: 'إدارة السياسات المؤسسية',
          permission: PERMISSIONS.POLICIES_VIEW
        },
        {
          name: 'إدارة الامتثال',
          href: ROUTES.COMPLIANCE,
          description: 'متابعة متطلبات الامتثال',
          permission: PERMISSIONS.COMPLIANCE_VIEW
        }
      ]
    },
    {
      name: 'التقارير والتحليلات',
      icon: ChartBarIcon,
      type: 'group',
      key: 'reports',
      description: 'تقارير شاملة وتحليلات متقدمة',
      permission: PERMISSIONS.REPORTS_VIEW,
      children: [
        {
          name: 'لوحة التقارير',
          href: ROUTES.REPORTS_DASHBOARD,
          description: 'نظرة عامة على التقارير',
          permission: PERMISSIONS.REPORTS_VIEW
        },
        {
          name: 'تقارير الحوادث',
          href: ROUTES.REPORTS_INCIDENTS,
          description: 'تقارير مفصلة للحوادث',
          permission: PERMISSIONS.REPORTS_VIEW
        },
        {
          name: 'تقارير المخاطر',
          href: ROUTES.REPORTS_RISKS,
          description: 'تحليلات المخاطر',
          permission: PERMISSIONS.REPORTS_VIEW
        }
      ]
    }
  ]

  // إضافة إدارة المستخدمين للمديرين
  if (user?.role === 'ADMIN' || hasPermission(user?.role, PERMISSIONS.USERS_VIEW)) {
    navigation.splice(1, 0, {
      name: 'إدارة المستخدمين',
      icon: UsersIcon,
      type: 'group',
      key: 'users',
      description: 'إدارة المستخدمين والصلاحيات',
      permission: PERMISSIONS.USERS_VIEW,
      children: [
        {
          name: 'جميع المستخدمين',
          href: ROUTES.USERS,
          description: 'عرض وإدارة جميع المستخدمين',
          permission: PERMISSIONS.USERS_VIEW
        },
        {
          name: 'إضافة مستخدم',
          href: ROUTES.USER_CREATE,
          description: 'إضافة مستخدم جديد للنظام',
          permission: PERMISSIONS.USERS_CREATE
        }
      ]
    })
  }

  const isCurrentPath = (path) => {
    return location.pathname === path
  }

  const isGroupActive = (group) => {
    if (!group.children) return false
    return group.children.some(child => 
      child.href && location.pathname.startsWith(child.href)
    )
  }

  const shouldExpandGroup = (groupKey) => {
    const group = navigation.find(nav => nav.key === groupKey)
    return expandedMenus[groupKey] || isGroupActive(group)
  }

  const NavigationItem = ({ item, isMobile = false }) => {
    // التحقق من الصلاحيات
    if (item.permission && !hasPermission(user?.role, item.permission)) {
      return null
    }

    if (item.type === 'single') {
      return (
        <Link
          to={item.href}
          onClick={isMobile ? () => setSidebarOpen(false) : undefined}
          className={`sidebar-nav-item interactive-element relative flex items-center px-4 py-3 text-sm font-medium transition-all duration-300 ${
            isCurrentPath(item.href)
              ? 'active text-white'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          <item.icon className="nav-icon w-5 h-5 ml-3" />
          <span className="sidebar-text">{item.name}</span>
        </Link>
      )
    }

    if (item.type === 'group') {
      const isExpanded = shouldExpandGroup(item.key)
      const isActive = isGroupActive(item)
      
      // فلترة العناصر الفرعية حسب الصلاحيات
      const visibleChildren = item.children?.filter(child => 
        !child.permission || hasPermission(user?.role, child.permission)
      ) || []

      // إخفاء المجموعة إذا لم تكن هناك عناصر فرعية مرئية
      if (visibleChildren.length === 0) {
        return null
      }

      return (
        <div className={`nav-group ${isExpanded ? 'nav-group-expanded' : ''}`}>
          <button
            onClick={() => toggleMenu(item.key)}
            className={`sidebar-nav-item interactive-element w-full flex items-center justify-between px-4 py-3 text-sm font-medium transition-all duration-300 ${
              isActive
                ? 'active text-white'
                : 'text-gray-300 hover:text-white'
            }`}
          >
            <div className="flex items-center">
              <item.icon className="nav-icon w-5 h-5 ml-3" />
              <span className="sidebar-text">{item.name}</span>
            </div>
            <div className="transition-transform duration-300">
              {isExpanded ? (
                <ChevronUpIcon className="w-4 h-4" />
              ) : (
                <ChevronDownIcon className="w-4 h-4" />
              )}
            </div>
          </button>

          {isExpanded && (
            <div className="nav-group-children animate-fade-in-up">
              <div className="py-2">
                {visibleChildren.map((child) => (
                  <Link
                    key={child.href}
                    to={child.href}
                    onClick={isMobile ? () => setSidebarOpen(false) : undefined}
                    className={`nav-child-item interactive-element relative block px-6 py-2 text-sm transition-all duration-300 ${
                      isCurrentPath(child.href)
                        ? 'active text-white bg-blue-600/20'
                        : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    <span className="mr-6">{child.name}</span>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      )
    }

    return null
  }

  return (
    <div className="min-h-screen bg-gray-900 flex" dir="rtl">
      {/* Overlay للشاشات الصغيرة */}
      {sidebarOpen && (
        <div 
          className="sidebar-overlay md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* القائمة الجانبية اليمنى */}
      <div className={`sidebar-professional fixed right-0 top-0 h-full w-80 z-50 transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* رأس القائمة الجانبية */}
        <div className="sidebar-header p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center ml-3">
                <ShieldExclamationIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="sidebar-title text-lg font-bold">نظام GRC</h1>
                <p className="text-blue-200 text-xs">الحوكمة والمخاطر والامتثال</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="md:hidden text-white hover:text-blue-200 transition-colors"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* التنقل */}
        <nav className="custom-scrollbar flex-1 overflow-y-auto px-2 py-4">
          <div className="space-y-1">
            {navigation.map((item) => (
              <NavigationItem key={item.name || item.key} item={item} isMobile={sidebarOpen} />
            ))}
          </div>
        </nav>

        {/* معلومات المستخدم */}
        <div className="border-t border-gray-700 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center ml-3">
              <UserCircleIcon className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {user?.full_name || user?.username}
              </p>
              <p className="text-xs text-gray-400 truncate">
                {user?.email}
              </p>
            </div>
            <button
              onClick={onLogout}
              className="text-gray-400 hover:text-white transition-colors"
              title="تسجيل الخروج"
            >
              <ArrowRightOnRectangleIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* شريط علوي */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <Bars3Icon className="w-6 h-6" />
                </button>
                
                {/* شريط البحث */}
                <div className="hidden md:block mr-4">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="البحث..."
                      className="w-64 pr-10 pl-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                {/* الإشعارات */}
                <button className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 relative">
                  <BellIcon className="w-6 h-6" />
                  <span className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full"></span>
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* المحتوى */}
        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
          <div className="p-6">
            <Breadcrumb />
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default ProfessionalLayout
