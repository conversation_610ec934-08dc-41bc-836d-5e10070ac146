import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { ArrowRightIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { createRisk, updateRisk, getRisk } from '../services/risks'
import { getUsers } from '../services/users'
import { RISK_TYPES, RISK_PROBABILITY, RISK_IMPACT } from '../utils/constants'

const RiskForm = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEdit = Boolean(id)
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [users, setUsers] = useState([])
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    risk_type: '',
    probability: '',
    impact: '',
    category: '',
    source: '',
    triggers: '',
    owner_id: '',
    responsible_id: '',
    next_review_date: ''
  })

  // جلب المستخدمين
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await getUsers({ per_page: 100 })
        setUsers(response.data)
      } catch (err) {
        console.error('Error fetching users:', err)
      }
    }
    fetchUsers()
  }, [])

  // جلب بيانات المخاطرة للتعديل
  useEffect(() => {
    if (isEdit) {
      const fetchRisk = async () => {
        try {
          setLoading(true)
          const risk = await getRisk(id)
          setFormData({
            title: risk.title || '',
            description: risk.description || '',
            risk_type: risk.risk_type || '',
            probability: risk.probability || '',
            impact: risk.impact || '',
            category: risk.category || '',
            source: risk.source || '',
            triggers: risk.triggers || '',
            owner_id: risk.owner_id || '',
            responsible_id: risk.responsible_id || '',
            next_review_date: risk.next_review_date ? risk.next_review_date.split('T')[0] : ''
          })
        } catch (err) {
          setError('فشل في جلب بيانات المخاطرة')
          console.error('Error fetching risk:', err)
        } finally {
          setLoading(false)
        }
      }
      fetchRisk()
    }
  }, [id, isEdit])

  // معالجة تغيير الحقول
  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // حساب درجة المخاطرة
  const calculateRiskScore = () => {
    const probabilityValues = {
      'VERY_LOW': 1,
      'LOW': 2,
      'MEDIUM': 3,
      'HIGH': 4,
      'VERY_HIGH': 5
    }
    
    const impactValues = {
      'VERY_LOW': 1,
      'LOW': 2,
      'MEDIUM': 3,
      'HIGH': 4,
      'VERY_HIGH': 5
    }
    
    const probValue = probabilityValues[formData.probability] || 0
    const impactValue = impactValues[formData.impact] || 0
    
    return probValue * impactValue
  }

  // تحديد مستوى المخاطرة
  const getRiskLevel = () => {
    const score = calculateRiskScore()
    if (score <= 4) return 'منخفض جداً'
    if (score <= 9) return 'منخفض'
    if (score <= 14) return 'متوسط'
    if (score <= 19) return 'عالي'
    return 'عالي جداً'
  }

  // معالجة الإرسال
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // التحقق من الحقول المطلوبة
      if (!formData.title || !formData.risk_type || !formData.probability || !formData.impact || !formData.owner_id) {
        setError('يرجى ملء جميع الحقول المطلوبة')
        return
      }

      const submitData = {
        ...formData,
        next_review_date: formData.next_review_date || null
      }

      if (isEdit) {
        await updateRisk(id, submitData)
      } else {
        await createRisk(submitData)
      }

      navigate('/risks')
    } catch (err) {
      setError(isEdit ? 'فشل في تحديث المخاطرة' : 'فشل في إنشاء المخاطرة')
      console.error('Error saving risk:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading && isEdit) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/risks')}
          className="text-gray-400 hover:text-white"
        >
          <ArrowRightIcon className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-white">
            {isEdit ? 'تعديل المخاطرة' : 'إضافة مخاطرة جديدة'}
          </h1>
          <p className="text-gray-400 mt-1">
            {isEdit ? 'تحديث بيانات المخاطرة' : 'إضافة مخاطرة جديدة إلى النظام'}
          </p>
        </div>
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg flex items-center gap-2">
          <ExclamationTriangleIcon className="h-5 w-5" />
          {error}
        </div>
      )}

      {/* النموذج */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-dark-800 rounded-lg p-6 space-y-6">
          {/* المعلومات الأساسية */}
          <div>
            <h3 className="text-lg font-medium text-white mb-4">المعلومات الأساسية</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* عنوان المخاطرة */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  عنوان المخاطرة *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                  className="input-field"
                  placeholder="أدخل عنوان المخاطرة"
                />
              </div>

              {/* نوع المخاطرة */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  نوع المخاطرة *
                </label>
                <select
                  name="risk_type"
                  value={formData.risk_type}
                  onChange={handleChange}
                  required
                  className="input-field"
                >
                  <option value="">اختر نوع المخاطرة</option>
                  {Object.entries(RISK_TYPES).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
              </div>

              {/* الفئة */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  الفئة الفرعية
                </label>
                <input
                  type="text"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="أدخل الفئة الفرعية"
                />
              </div>

              {/* الوصف */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  وصف المخاطرة
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={4}
                  className="input-field"
                  placeholder="أدخل وصف تفصيلي للمخاطرة"
                />
              </div>
            </div>
          </div>

          {/* التقييم */}
          <div>
            <h3 className="text-lg font-medium text-white mb-4">تقييم المخاطرة</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* الاحتمالية */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  الاحتمالية *
                </label>
                <select
                  name="probability"
                  value={formData.probability}
                  onChange={handleChange}
                  required
                  className="input-field"
                >
                  <option value="">اختر الاحتمالية</option>
                  {Object.entries(RISK_PROBABILITY).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
              </div>

              {/* التأثير */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  التأثير *
                </label>
                <select
                  name="impact"
                  value={formData.impact}
                  onChange={handleChange}
                  required
                  className="input-field"
                >
                  <option value="">اختر التأثير</option>
                  {Object.entries(RISK_IMPACT).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
              </div>

              {/* مستوى المخاطرة المحسوب */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  مستوى المخاطرة
                </label>
                <div className="input-field bg-dark-700 text-gray-400">
                  {formData.probability && formData.impact ? (
                    <span>
                      {getRiskLevel()} (درجة: {calculateRiskScore()})
                    </span>
                  ) : (
                    'سيتم حسابه تلقائياً'
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* معلومات إضافية */}
          <div>
            <h3 className="text-lg font-medium text-white mb-4">معلومات إضافية</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* المصدر */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  مصدر المخاطرة
                </label>
                <input
                  type="text"
                  name="source"
                  value={formData.source}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="أدخل مصدر المخاطرة"
                />
              </div>

              {/* تاريخ المراجعة التالية */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  تاريخ المراجعة التالية
                </label>
                <input
                  type="date"
                  name="next_review_date"
                  value={formData.next_review_date}
                  onChange={handleChange}
                  className="input-field"
                />
              </div>

              {/* المحفزات */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  محفزات المخاطرة
                </label>
                <textarea
                  name="triggers"
                  value={formData.triggers}
                  onChange={handleChange}
                  rows={3}
                  className="input-field"
                  placeholder="أدخل العوامل التي قد تؤدي إلى حدوث هذه المخاطرة"
                />
              </div>
            </div>
          </div>

          {/* المسؤوليات */}
          <div>
            <h3 className="text-lg font-medium text-white mb-4">المسؤوليات</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* مالك المخاطرة */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  مالك المخاطرة *
                </label>
                <select
                  name="owner_id"
                  value={formData.owner_id}
                  onChange={handleChange}
                  required
                  className="input-field"
                >
                  <option value="">اختر مالك المخاطرة</option>
                  {users.map(user => (
                    <option key={user.id} value={user.id}>
                      {user.full_name} ({user.username})
                    </option>
                  ))}
                </select>
              </div>

              {/* المسؤول عن المتابعة */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  المسؤول عن المتابعة
                </label>
                <select
                  name="responsible_id"
                  value={formData.responsible_id}
                  onChange={handleChange}
                  className="input-field"
                >
                  <option value="">اختر المسؤول عن المتابعة</option>
                  {users.map(user => (
                    <option key={user.id} value={user.id}>
                      {user.full_name} ({user.username})
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={() => navigate('/risks')}
            className="btn-secondary"
          >
            إلغاء
          </button>
          <button
            type="submit"
            disabled={loading}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'جاري الحفظ...' : (isEdit ? 'تحديث المخاطرة' : 'إضافة المخاطرة')}
          </button>
        </div>
      </form>
    </div>
  )
}

export default RiskForm
