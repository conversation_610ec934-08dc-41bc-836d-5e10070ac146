/* نظام التصميم الاحترافي لنظام GRC */

/* متغيرات الألوان الاحترافية */
:root {
  /* الألوان الأساسية */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* الألوان الثانوية */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* ألوان الخلفية الداكنة */
  --dark-50: #18181b;
  --dark-100: #27272a;
  --dark-200: #3f3f46;
  --dark-300: #52525b;
  --dark-400: #71717a;
  --dark-500: #a1a1aa;
  --dark-600: #d4d4d8;
  --dark-700: #e4e4e7;
  --dark-800: #f4f4f5;
  --dark-900: #fafafa;

  /* ألوان الحالة */
  --success-50: #ecfdf5;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;
  --info-700: #1d4ed8;

  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* الحدود */
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;

  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* الخطوط */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* الانتقالات */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* إعدادات عامة للتصميم الداكن */
.dark-theme {
  background-color: var(--dark-50);
  color: var(--dark-800);
}

/* القائمة الجانبية الاحترافية */
.sidebar-professional {
  background: linear-gradient(180deg, var(--dark-100) 0%, var(--dark-50) 100%);
  border-left: 1px solid var(--dark-200);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
}

.sidebar-header {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  border-bottom: 1px solid var(--primary-500);
}

.sidebar-nav-item {
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-xs) var(--spacing-md);
}

.sidebar-nav-item:hover {
  background: linear-gradient(135deg, var(--dark-200) 0%, var(--dark-100) 100%);
  transform: translateX(-2px);
  box-shadow: var(--shadow-md);
}

.sidebar-nav-item.active {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  box-shadow: var(--shadow-lg);
}

.sidebar-nav-item.active::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: var(--primary-400);
  border-radius: var(--border-radius-sm);
}

/* أيقونات احترافية */
.nav-icon {
  transition: all var(--transition-normal);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.sidebar-nav-item:hover .nav-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.sidebar-nav-item.active .nav-icon {
  color: var(--primary-100);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* تأثيرات التوسع للمجموعات */
.nav-group-expanded {
  background: var(--dark-200);
  border-radius: var(--border-radius-lg);
}

.nav-group-children {
  background: linear-gradient(180deg, transparent 0%, var(--dark-100) 100%);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  border-top: 1px solid var(--dark-300);
}

.nav-child-item {
  position: relative;
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-xs) var(--spacing-md);
}

.nav-child-item::before {
  content: '';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: var(--dark-400);
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.nav-child-item:hover::before {
  background: var(--primary-400);
  transform: translateY(-50%) scale(1.5);
}

.nav-child-item.active::before {
  background: var(--primary-300);
  transform: translateY(-50%) scale(1.5);
  box-shadow: 0 0 8px var(--primary-400);
}

/* تحسينات الطباعة */
.sidebar-text {
  font-weight: 500;
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sidebar-title {
  font-weight: 700;
  font-size: var(--font-size-lg);
  background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slideInRight var(--transition-normal) ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp var(--transition-normal) ease-out;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .sidebar-professional {
    transform: translateX(100%);
    transition: transform var(--transition-normal);
  }
  
  .sidebar-professional.open {
    transform: translateX(0);
  }
  
  .sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 40;
  }
}

/* تحسينات إضافية للتفاعل */
.interactive-element {
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.interactive-element:active {
  transform: scale(0.98);
}

/* شريط التمرير المخصص */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--dark-100);
  border-radius: var(--border-radius-sm);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--dark-300);
  border-radius: var(--border-radius-sm);
  transition: background var(--transition-normal);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--dark-400);
}

/* تأثيرات الإضاءة */
.glow-effect {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}
