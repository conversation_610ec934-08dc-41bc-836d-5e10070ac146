"""
إضافة حقول جديدة لجدول المستخدمين
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '002_add_user_fields'
down_revision = '001_initial'
branch_labels = None
depends_on = None

def upgrade():
    """إضافة الحقول الجديدة"""
    
    # إضافة الأدوار الجديدة
    op.execute("""
        ALTER TYPE userrole ADD VALUE IF NOT EXISTS 'RISK_MANAGER';
        ALTER TYPE userrole ADD VALUE IF NOT EXISTS 'COMPLIANCE_OFFICER';
        ALTER TYPE userrole ADD VALUE IF NOT EXISTS 'AUDITOR';
    """)
    
    # تحديث القيم الموجودة
    op.execute("""
        UPDATE users SET role = 'ADMIN' WHERE role = 'admin';
        UPDATE users SET role = 'USER' WHERE role = 'user';
    """)
    
    # إضافة الحقول الجديدة
    op.add_column('users', sa.Column('last_login', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('password_changed_at', sa.DateTime(timezone=True), server_default=sa.func.now()))
    op.add_column('users', sa.Column('failed_login_attempts', sa.Integer(), default=0, nullable=False))
    op.add_column('users', sa.Column('locked_until', sa.DateTime(timezone=True), nullable=True))
    
    # جعل البريد الإلكتروني مطلوب
    op.alter_column('users', 'email', nullable=False)

def downgrade():
    """إزالة الحقول الجديدة"""
    
    # إزالة الحقول المضافة
    op.drop_column('users', 'locked_until')
    op.drop_column('users', 'failed_login_attempts')
    op.drop_column('users', 'password_changed_at')
    op.drop_column('users', 'last_login')
    
    # إرجاع البريد الإلكتروني لاختياري
    op.alter_column('users', 'email', nullable=True)
    
    # إرجاع القيم القديمة للأدوار
    op.execute("""
        UPDATE users SET role = 'admin' WHERE role = 'ADMIN';
        UPDATE users SET role = 'user' WHERE role IN ('USER', 'RISK_MANAGER', 'COMPLIANCE_OFFICER', 'AUDITOR');
    """)
