"""
APIs إدارة المخاطر
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from typing import List, Optional
from datetime import datetime, timedelta

from ..database import get_db
from ..core.deps import get_current_user
from ..models.user import User, UserRole
from ..models.risk import Risk, RiskType, RiskLevel, RiskStatus, RiskProbability, RiskImpact
from ..models.mitigation import MitigationPlan, MitigationStatus
from ..schemas.risk import (
    RiskCreate, RiskUpdate, Risk as RiskSchema, RiskWithDetails,
    RiskListResponse, RiskStatistics, RiskMatrixData, RiskFilter,
    RiskAssessment, RiskMatrixPosition
)
from ..utils.permissions import check_permission

router = APIRouter()


@router.get("/", response_model=RiskListResponse)
async def get_risks(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    risk_type: Optional[RiskType] = Query(None),
    risk_level: Optional[RiskLevel] = Query(None),
    status: Optional[RiskStatus] = Query(None),
    owner_id: Optional[int] = Query(None),
    responsible_id: Optional[int] = Query(None),
    overdue_only: bool = Query(False),
    high_priority_only: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة المخاطر مع الفلترة والبحث"""
    
    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_VIEW"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لعرض المخاطر")
    
    # بناء الاستعلام الأساسي
    query = db.query(Risk).options(
        joinedload(Risk.owner),
        joinedload(Risk.responsible),
        joinedload(Risk.mitigation_plans)
    )
    
    # تطبيق الفلاتر
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Risk.title.ilike(search_term),
                Risk.description.ilike(search_term),
                Risk.category.ilike(search_term),
                Risk.source.ilike(search_term)
            )
        )
    
    if risk_type:
        query = query.filter(Risk.risk_type == risk_type)
    
    if risk_level:
        query = query.filter(Risk.risk_level == risk_level)
    
    if status:
        query = query.filter(Risk.status == status)
    
    if owner_id:
        query = query.filter(Risk.owner_id == owner_id)
    
    if responsible_id:
        query = query.filter(Risk.responsible_id == responsible_id)
    
    if high_priority_only:
        query = query.filter(Risk.risk_level.in_([RiskLevel.HIGH, RiskLevel.VERY_HIGH]))
    
    if overdue_only:
        current_date = datetime.utcnow()
        query = query.filter(
            and_(
                Risk.next_review_date.isnot(None),
                Risk.next_review_date < current_date,
                Risk.status.notin_([RiskStatus.CLOSED])
            )
        )
    
    # حساب العدد الإجمالي
    total = query.count()
    
    # تطبيق التصفح
    offset = (page - 1) * per_page
    risks = query.order_by(desc(Risk.created_at)).offset(offset).limit(per_page).all()
    
    # تحويل البيانات
    risks_data = []
    for risk in risks:
        risk_dict = RiskWithDetails.from_orm(risk).dict()
        
        # إضافة معلومات إضافية
        risk_dict['owner_name'] = risk.owner.full_name if risk.owner else None
        risk_dict['responsible_name'] = risk.responsible.full_name if risk.responsible else None
        risk_dict['mitigation_plans_count'] = len(risk.mitigation_plans)
        risk_dict['active_mitigation_plans'] = len([
            plan for plan in risk.mitigation_plans 
            if plan.status in [MitigationStatus.PLANNED, MitigationStatus.IN_PROGRESS]
        ])
        
        # حساب الإجراءات المتأخرة
        overdue_actions = 0
        for plan in risk.mitigation_plans:
            overdue_actions += len([
                action for action in plan.actions 
                if action.is_overdue
            ])
        risk_dict['overdue_actions'] = overdue_actions
        
        # موقع المصفوفة
        risk_dict['matrix_position'] = risk.risk_matrix_position
        
        risks_data.append(RiskWithDetails(**risk_dict))
    
    return RiskListResponse(
        data=risks_data,
        total=total,
        page=page,
        per_page=per_page
    )


@router.post("/", response_model=RiskSchema)
async def create_risk(
    risk_data: RiskCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء مخاطرة جديدة"""
    
    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_CREATE"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لإنشاء المخاطر")
    
    # التحقق من وجود المالك
    owner = db.query(User).filter(User.id == risk_data.owner_id).first()
    if not owner:
        raise HTTPException(status_code=404, detail="المالك المحدد غير موجود")
    
    # التحقق من وجود المسؤول (إذا تم تحديده)
    if risk_data.responsible_id:
        responsible = db.query(User).filter(User.id == risk_data.responsible_id).first()
        if not responsible:
            raise HTTPException(status_code=404, detail="المسؤول المحدد غير موجود")
    
    # إنشاء المخاطرة
    risk = Risk(**risk_data.dict())
    
    # حساب درجة المخاطرة
    risk.calculate_risk_score()
    
    # تحديد تاريخ المراجعة التالية (افتراضياً بعد 30 يوم)
    if not risk.next_review_date:
        risk.next_review_date = datetime.utcnow() + timedelta(days=30)
    
    db.add(risk)
    db.commit()
    db.refresh(risk)
    
    return RiskSchema.from_orm(risk)


@router.get("/{risk_id}", response_model=RiskWithDetails)
async def get_risk(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على تفاصيل مخاطرة محددة"""
    
    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_VIEW"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لعرض المخاطر")
    
    risk = db.query(Risk).options(
        joinedload(Risk.owner),
        joinedload(Risk.responsible),
        joinedload(Risk.mitigation_plans)
    ).filter(Risk.id == risk_id).first()
    
    if not risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")
    
    # تحويل البيانات
    risk_dict = RiskWithDetails.from_orm(risk).dict()
    risk_dict['owner_name'] = risk.owner.full_name if risk.owner else None
    risk_dict['responsible_name'] = risk.responsible.full_name if risk.responsible else None
    risk_dict['mitigation_plans_count'] = len(risk.mitigation_plans)
    risk_dict['active_mitigation_plans'] = len([
        plan for plan in risk.mitigation_plans 
        if plan.status in [MitigationStatus.PLANNED, MitigationStatus.IN_PROGRESS]
    ])
    
    # حساب الإجراءات المتأخرة
    overdue_actions = 0
    for plan in risk.mitigation_plans:
        overdue_actions += len([
            action for action in plan.actions 
            if action.is_overdue
        ])
    risk_dict['overdue_actions'] = overdue_actions
    risk_dict['matrix_position'] = risk.risk_matrix_position
    
    return RiskWithDetails(**risk_dict)


@router.put("/{risk_id}", response_model=RiskSchema)
async def update_risk(
    risk_id: int,
    risk_data: RiskUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث مخاطرة موجودة"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_EDIT"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لتعديل المخاطر")

    risk = db.query(Risk).filter(Risk.id == risk_id).first()
    if not risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")

    # التحقق من وجود المسؤول الجديد (إذا تم تحديده)
    if risk_data.responsible_id:
        responsible = db.query(User).filter(User.id == risk_data.responsible_id).first()
        if not responsible:
            raise HTTPException(status_code=404, detail="المسؤول المحدد غير موجود")

    # تحديث البيانات
    update_data = risk_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(risk, field, value)

    # إعادة حساب درجة المخاطرة إذا تم تغيير الاحتمالية أو التأثير
    if risk_data.probability or risk_data.impact:
        risk.calculate_risk_score()
        risk.last_assessment_date = datetime.utcnow()

    db.commit()
    db.refresh(risk)

    return RiskSchema.from_orm(risk)


@router.delete("/{risk_id}")
async def delete_risk(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف مخاطرة"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_DELETE"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لحذف المخاطر")

    risk = db.query(Risk).filter(Risk.id == risk_id).first()
    if not risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")

    # التحقق من وجود خطط تخفيف نشطة
    active_plans = db.query(MitigationPlan).filter(
        and_(
            MitigationPlan.risk_id == risk_id,
            MitigationPlan.status.in_([MitigationStatus.PLANNED, MitigationStatus.IN_PROGRESS])
        )
    ).count()

    if active_plans > 0:
        raise HTTPException(
            status_code=400,
            detail="لا يمكن حذف المخاطرة لوجود خطط تخفيف نشطة"
        )

    db.delete(risk)
    db.commit()

    return {"message": "تم حذف المخاطرة بنجاح"}


@router.post("/{risk_id}/assess", response_model=RiskSchema)
async def assess_risk(
    risk_id: int,
    assessment: RiskAssessment,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تقييم مخاطرة"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_ASSESS"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لتقييم المخاطر")

    risk = db.query(Risk).filter(Risk.id == risk_id).first()
    if not risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")

    # تحديث التقييم
    risk.probability = assessment.probability
    risk.impact = assessment.impact
    risk.calculate_risk_score()
    risk.last_assessment_date = datetime.utcnow()
    risk.status = RiskStatus.ASSESSED

    # إضافة ملاحظات التقييم إذا وجدت
    if assessment.assessment_notes:
        if risk.description:
            risk.description += f"\n\nملاحظات التقييم ({datetime.utcnow().strftime('%Y-%m-%d')}): {assessment.assessment_notes}"
        else:
            risk.description = f"ملاحظات التقييم ({datetime.utcnow().strftime('%Y-%m-%d')}): {assessment.assessment_notes}"

    db.commit()
    db.refresh(risk)

    return RiskSchema.from_orm(risk)


@router.get("/statistics/overview", response_model=RiskStatistics)
async def get_risk_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على إحصائيات المخاطر"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_VIEW"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لعرض المخاطر")

    # العدد الإجمالي
    total_risks = db.query(Risk).count()

    # حسب المستوى
    by_level = {}
    for level in RiskLevel:
        count = db.query(Risk).filter(Risk.risk_level == level).count()
        by_level[level.value] = count

    # حسب النوع
    by_type = {}
    for risk_type in RiskType:
        count = db.query(Risk).filter(Risk.risk_type == risk_type).count()
        by_type[risk_type.value] = count

    # حسب الحالة
    by_status = {}
    for status in RiskStatus:
        count = db.query(Risk).filter(Risk.status == status).count()
        by_status[status.value] = count

    # المخاطر عالية الأولوية
    high_priority_risks = db.query(Risk).filter(
        Risk.risk_level.in_([RiskLevel.HIGH, RiskLevel.VERY_HIGH])
    ).count()

    # المراجعات المتأخرة
    current_date = datetime.utcnow()
    overdue_reviews = db.query(Risk).filter(
        and_(
            Risk.next_review_date.isnot(None),
            Risk.next_review_date < current_date,
            Risk.status.notin_([RiskStatus.CLOSED])
        )
    ).count()

    # التقييمات الحديثة (آخر 30 يوم)
    thirty_days_ago = current_date - timedelta(days=30)
    recent_assessments = db.query(Risk).filter(
        and_(
            Risk.last_assessment_date.isnot(None),
            Risk.last_assessment_date >= thirty_days_ago
        )
    ).count()

    return RiskStatistics(
        total_risks=total_risks,
        by_level=by_level,
        by_type=by_type,
        by_status=by_status,
        high_priority_risks=high_priority_risks,
        overdue_reviews=overdue_reviews,
        recent_assessments=recent_assessments
    )


@router.get("/matrix/data", response_model=RiskMatrixData)
async def get_risk_matrix_data(
    risk_type: Optional[RiskType] = Query(None),
    status: Optional[RiskStatus] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على بيانات مصفوفة المخاطر"""

    # التحقق من الصلاحيات
    if not check_permission(current_user.role, "RISKS_VIEW"):
        raise HTTPException(status_code=403, detail="ليس لديك صلاحية لعرض المخاطر")

    # بناء الاستعلام
    query = db.query(Risk).options(joinedload(Risk.owner))

    if risk_type:
        query = query.filter(Risk.risk_type == risk_type)

    if status:
        query = query.filter(Risk.status == status)

    risks = query.all()

    # تحويل البيانات للمصفوفة
    matrix_risks = []
    for risk in risks:
        matrix_risks.append({
            "id": risk.id,
            "title": risk.title,
            "type": risk.risk_type.value,
            "level": risk.risk_level.value if risk.risk_level else "VERY_LOW",
            "status": risk.status.value,
            "owner": risk.owner.full_name if risk.owner else "غير محدد",
            "position": risk.risk_matrix_position,
            "score": risk.risk_score or 0
        })

    # إعداد المصفوفة
    matrix_config = {
        "probability_labels": ["منخفض جداً", "منخفض", "متوسط", "عالي", "عالي جداً"],
        "impact_labels": ["منخفض جداً", "منخفض", "متوسط", "عالي", "عالي جداً"],
        "risk_levels": {
            "VERY_LOW": {"color": "#22c55e", "label": "منخفض جداً"},
            "LOW": {"color": "#84cc16", "label": "منخفض"},
            "MEDIUM": {"color": "#eab308", "label": "متوسط"},
            "HIGH": {"color": "#f97316", "label": "عالي"},
            "VERY_HIGH": {"color": "#ef4444", "label": "عالي جداً"}
        }
    }

    # الإحصائيات
    statistics = await get_risk_statistics(db, current_user)

    return RiskMatrixData(
        risks=matrix_risks,
        matrix_config=matrix_config,
        statistics=statistics
    )
