"""
نموذج المخاطر
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, Float, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..database import Base


class RiskType(str, enum.Enum):
    """أنواع المخاطر"""
    OPERATIONAL = "OPERATIONAL"           # تشغيلية
    FINANCIAL = "FINANCIAL"               # مالية
    STRATEGIC = "STRATEGIC"               # استراتيجية
    COMPLIANCE = "COMPLIANCE"             # امتثال
    REPUTATIONAL = "REPUTATIONAL"         # سمعة
    TECHNOLOGY = "TECHNOLOGY"             # تقنية
    CYBERSECURITY = "CYBERSECURITY"       # أمن سيبراني
    LEGAL = "LEGAL"                       # قانونية
    ENVIRONMENTAL = "ENVIRONMENTAL"       # بيئية
    MARKET = "MARKET"                     # سوق


class RiskLevel(str, enum.Enum):
    """مستويات المخاطر"""
    VERY_LOW = "VERY_LOW"                 # منخفض جداً (1-4)
    LOW = "LOW"                           # منخفض (5-9)
    MEDIUM = "MEDIUM"                     # متوسط (10-14)
    HIGH = "HIGH"                         # عالي (15-19)
    VERY_HIGH = "VERY_HIGH"               # عالي جداً (20-25)


class RiskStatus(str, enum.Enum):
    """حالات المخاطر"""
    IDENTIFIED = "IDENTIFIED"             # محدد
    ASSESSED = "ASSESSED"                 # مقيم
    MITIGATED = "MITIGATED"               # مخفف
    MONITORED = "MONITORED"               # مراقب
    CLOSED = "CLOSED"                     # مغلق
    ESCALATED = "ESCALATED"               # مصعد


class RiskProbability(str, enum.Enum):
    """احتمالية حدوث المخاطر"""
    VERY_LOW = "VERY_LOW"                 # منخفض جداً (1)
    LOW = "LOW"                           # منخفض (2)
    MEDIUM = "MEDIUM"                     # متوسط (3)
    HIGH = "HIGH"                         # عالي (4)
    VERY_HIGH = "VERY_HIGH"               # عالي جداً (5)


class RiskImpact(str, enum.Enum):
    """تأثير المخاطر"""
    VERY_LOW = "VERY_LOW"                 # منخفض جداً (1)
    LOW = "LOW"                           # منخفض (2)
    MEDIUM = "MEDIUM"                     # متوسط (3)
    HIGH = "HIGH"                         # عالي (4)
    VERY_HIGH = "VERY_HIGH"               # عالي جداً (5)


class Risk(Base):
    """نموذج المخاطر"""
    __tablename__ = "risks"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    risk_type = Column(Enum(RiskType), nullable=False)
    
    # تقييم المخاطر
    probability = Column(Enum(RiskProbability), nullable=False)
    impact = Column(Enum(RiskImpact), nullable=False)
    risk_score = Column(Float, nullable=True)  # محسوب تلقائياً من الاحتمالية والتأثير
    risk_level = Column(Enum(RiskLevel), nullable=True)  # محسوب تلقائياً
    
    # معلومات إضافية
    category = Column(String(100), nullable=True)  # فئة فرعية
    source = Column(String(200), nullable=True)    # مصدر المخاطرة
    triggers = Column(Text, nullable=True)         # محفزات المخاطرة
    
    # المسؤوليات
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # مالك المخاطرة
    responsible_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # المسؤول عن المتابعة
    
    # الحالة والتواريخ
    status = Column(Enum(RiskStatus), default=RiskStatus.IDENTIFIED)
    identified_date = Column(DateTime(timezone=True), server_default=func.now())
    last_assessment_date = Column(DateTime(timezone=True), nullable=True)
    next_review_date = Column(DateTime(timezone=True), nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    owner = relationship("User", foreign_keys=[owner_id], back_populates="owned_risks")
    responsible = relationship("User", foreign_keys=[responsible_id], back_populates="responsible_risks")
    mitigation_plans = relationship("MitigationPlan", back_populates="risk", cascade="all, delete-orphan")
    
    def calculate_risk_score(self):
        """حساب درجة المخاطرة"""
        probability_values = {
            RiskProbability.VERY_LOW: 1,
            RiskProbability.LOW: 2,
            RiskProbability.MEDIUM: 3,
            RiskProbability.HIGH: 4,
            RiskProbability.VERY_HIGH: 5
        }
        
        impact_values = {
            RiskImpact.VERY_LOW: 1,
            RiskImpact.LOW: 2,
            RiskImpact.MEDIUM: 3,
            RiskImpact.HIGH: 4,
            RiskImpact.VERY_HIGH: 5
        }
        
        prob_value = probability_values.get(self.probability, 1)
        impact_value = impact_values.get(self.impact, 1)
        
        self.risk_score = prob_value * impact_value
        
        # تحديد مستوى المخاطرة بناءً على النتيجة
        if self.risk_score <= 4:
            self.risk_level = RiskLevel.VERY_LOW
        elif self.risk_score <= 9:
            self.risk_level = RiskLevel.LOW
        elif self.risk_score <= 14:
            self.risk_level = RiskLevel.MEDIUM
        elif self.risk_score <= 19:
            self.risk_level = RiskLevel.HIGH
        else:
            self.risk_level = RiskLevel.VERY_HIGH
        
        return self.risk_score
    
    @property
    def risk_matrix_position(self):
        """موقع المخاطرة في المصفوفة"""
        probability_values = {
            RiskProbability.VERY_LOW: 1,
            RiskProbability.LOW: 2,
            RiskProbability.MEDIUM: 3,
            RiskProbability.HIGH: 4,
            RiskProbability.VERY_HIGH: 5
        }
        
        impact_values = {
            RiskImpact.VERY_LOW: 1,
            RiskImpact.LOW: 2,
            RiskImpact.MEDIUM: 3,
            RiskImpact.HIGH: 4,
            RiskImpact.VERY_HIGH: 5
        }
        
        return {
            "probability": probability_values.get(self.probability, 1),
            "impact": impact_values.get(self.impact, 1),
            "score": self.risk_score or 0,
            "level": self.risk_level.value if self.risk_level else "VERY_LOW"
        }
