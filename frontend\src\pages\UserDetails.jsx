import React, { useState, useEffect } from 'react'
import { useNavigate, usePara<PERSON>, Link } from 'react-router-dom'
import { 
  UserIcon, 
  PencilIcon, 
  TrashIcon, 
  CheckIcon, 
  XMarkIcon,
  EnvelopeIcon,
  CalendarIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { ROUTES } from '../utils/constants'
import usersService from '../services/users'
import Breadcrumb from '../components/common/Breadcrumb'

const UserDetails = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleting, setDeleting] = useState(false)

  // تحميل بيانات المستخدم
  useEffect(() => {
    const loadUser = async () => {
      try {
        setLoading(true)
        const userData = await usersService.getUserById(id)
        setUser(userData)
      } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدم:', error)
        setError('فشل في تحميل بيانات المستخدم')
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      loadUser()
    }
  }, [id])

  const handleDelete = async () => {
    try {
      setDeleting(true)
      await usersService.deleteUser(id)
      navigate(ROUTES.USERS)
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error)
      setError('فشل في حذف المستخدم')
    } finally {
      setDeleting(false)
      setShowDeleteModal(false)
    }
  }

  const handleToggleStatus = async () => {
    try {
      await usersService.toggleUserStatus(id)
      setUser(prev => ({ ...prev, is_active: !prev.is_active }))
    } catch (error) {
      console.error('خطأ في تغيير حالة المستخدم:', error)
      setError('فشل في تغيير حالة المستخدم')
    }
  }

  const getRoleDisplayName = (role) => {
    const roleNames = {
      'ADMIN': 'مدير',
      'USER': 'مستخدم',
      'RISK_MANAGER': 'مدير مخاطر',
      'COMPLIANCE_OFFICER': 'مسؤول امتثال',
      'AUDITOR': 'مدقق'
    }
    return roleNames[role] || role
  }

  const getRoleColor = (role) => {
    const roleColors = {
      'ADMIN': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'USER': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      'RISK_MANAGER': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
      'COMPLIANCE_OFFICER': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'AUDITOR': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
    }
    return roleColors[role] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }

  const breadcrumbItems = [
    { label: 'إدارة المستخدمين', href: ROUTES.USERS },
    { label: user ? user.full_name : 'تفاصيل المستخدم' }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <Breadcrumb customItems={breadcrumbItems} />
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
            <div className="text-center">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                {error || 'المستخدم غير موجود'}
              </h3>
              <div className="mt-6">
                <Link
                  to={ROUTES.USERS}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  العودة إلى قائمة المستخدمين
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <Breadcrumb customItems={breadcrumbItems} />
        
        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    <UserIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
                <div className="mr-4">
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {user.full_name}
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    @{user.username}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.is_active 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                }`}>
                  {user.is_active ? 'نشط' : 'غير نشط'}
                </span>
                
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                  {getRoleDisplayName(user.role)}
                </span>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* معلومات أساسية */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  المعلومات الأساسية
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-center">
                    <UserIcon className="h-5 w-5 text-gray-400 ml-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">اسم المستخدم</p>
                      <p className="text-sm text-gray-900 dark:text-white">{user.username}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400 ml-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                      <p className="text-sm text-gray-900 dark:text-white">{user.email}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <ShieldCheckIcon className="h-5 w-5 text-gray-400 ml-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">الدور</p>
                      <p className="text-sm text-gray-900 dark:text-white">{getRoleDisplayName(user.role)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* معلومات النظام */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  معلومات النظام
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-center">
                    <CalendarIcon className="h-5 w-5 text-gray-400 ml-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">تاريخ الإنشاء</p>
                      <p className="text-sm text-gray-900 dark:text-white">
                        {new Date(user.created_at).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <CalendarIcon className="h-5 w-5 text-gray-400 ml-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">آخر تحديث</p>
                      <p className="text-sm text-gray-900 dark:text-white">
                        {new Date(user.updated_at).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <div className={`h-3 w-3 rounded-full ml-3 ${
                      user.is_active ? 'bg-green-400' : 'bg-red-400'
                    }`} />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">الحالة</p>
                      <p className="text-sm text-gray-900 dark:text-white">
                        {user.is_active ? 'نشط' : 'غير نشط'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
            <div className="flex justify-between">
              <div className="flex space-x-3 space-x-reverse">
                <Link
                  to={`/users/edit/${user.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PencilIcon className="h-4 w-4 ml-2" />
                  تعديل
                </Link>
                
                <button
                  onClick={handleToggleStatus}
                  className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${
                    user.is_active 
                      ? 'bg-red-600 hover:bg-red-700'
                      : 'bg-green-600 hover:bg-green-700'
                  }`}
                >
                  {user.is_active ? (
                    <>
                      <XMarkIcon className="h-4 w-4 ml-2" />
                      إلغاء التفعيل
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 ml-2" />
                      تفعيل
                    </>
                  )}
                </button>
              </div>
              
              <button
                onClick={() => setShowDeleteModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
              >
                <TrashIcon className="h-4 w-4 ml-2" />
                حذف
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mt-4">
                حذف المستخدم
              </h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  هل أنت متأكد من حذف المستخدم "{user.full_name}"؟ 
                  هذا الإجراء لا يمكن التراجع عنه.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-4 space-x-reverse">
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleDelete}
                    disabled={deleting}
                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 disabled:opacity-50"
                  >
                    {deleting ? 'جاري الحذف...' : 'حذف'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserDetails
