import React, { useState, useEffect } from 'react'
import { authUtils } from '../../services/api'
import { authService } from '../../services/auth'

const AuthDebug = () => {
  const [authInfo, setAuthInfo] = useState({})
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    checkAuthInfo()
  }, [])

  const checkAuthInfo = () => {
    const token = authUtils.getToken()
    const user = authUtils.getUser()
    const isAuth = authUtils.isAuthenticated()

    setAuthInfo({
      hasToken: !!token,
      token: token ? `${token.substring(0, 20)}...` : 'لا يوجد',
      user: user,
      isAuthenticated: isAuth,
      localStorage: {
        access_token: localStorage.getItem('access_token'),
        user: localStorage.getItem('user'),
        token: localStorage.getItem('token') // للتحقق من المفتاح القديم
      }
    })
  }

  const testCurrentUser = async () => {
    setIsLoading(true)
    try {
      const user = await authService.getCurrentUser()
      console.log('Current user:', user)
      alert(`تم الحصول على المستخدم بنجاح: ${user.username}`)
    } catch (error) {
      console.error('Error getting current user:', error)
      alert(`خطأ في الحصول على المستخدم: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testLogin = async () => {
    setIsLoading(true)
    try {
      const result = await authService.login({
        username: 'admin',
        password: 'Admin@123'
      })
      console.log('Login result:', result)
      checkAuthInfo() // تحديث المعلومات
      alert('تم تسجيل الدخول بنجاح!')
    } catch (error) {
      console.error('Login error:', error)
      alert(`خطأ في تسجيل الدخول: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const clearAuth = () => {
    authUtils.removeToken()
    localStorage.clear()
    checkAuthInfo()
    alert('تم مسح جميع بيانات التحقق من الهوية')
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 m-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        تشخيص التحقق من الهوية
      </h3>
      
      <div className="space-y-4">
        {/* معلومات التحقق من الهوية */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">حالة التحقق من الهوية:</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>يوجد توكن:</span>
              <span className={authInfo.hasToken ? 'text-green-600' : 'text-red-600'}>
                {authInfo.hasToken ? '✅ نعم' : '❌ لا'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>مصادق:</span>
              <span className={authInfo.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                {authInfo.isAuthenticated ? '✅ نعم' : '❌ لا'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>التوكن:</span>
              <span className="text-gray-600 dark:text-gray-400 font-mono text-xs">
                {authInfo.token}
              </span>
            </div>
            {authInfo.user && (
              <div className="flex justify-between">
                <span>المستخدم:</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {authInfo.user.username} ({authInfo.user.role})
                </span>
              </div>
            )}
          </div>
        </div>

        {/* localStorage Debug */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">localStorage:</h4>
          <div className="space-y-1 text-xs font-mono">
            <div>access_token: {authInfo.localStorage?.access_token ? '✅ موجود' : '❌ غير موجود'}</div>
            <div>user: {authInfo.localStorage?.user ? '✅ موجود' : '❌ غير موجود'}</div>
            <div>token (قديم): {authInfo.localStorage?.token ? '⚠️ موجود' : '✅ غير موجود'}</div>
          </div>
        </div>

        {/* أزرار الاختبار */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={checkAuthInfo}
            className="px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            تحديث المعلومات
          </button>
          
          <button
            onClick={testCurrentUser}
            disabled={isLoading}
            className="px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
          >
            {isLoading ? 'جاري...' : 'اختبار المستخدم الحالي'}
          </button>
          
          <button
            onClick={testLogin}
            disabled={isLoading}
            className="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 disabled:opacity-50"
          >
            {isLoading ? 'جاري...' : 'اختبار تسجيل الدخول'}
          </button>
          
          <button
            onClick={clearAuth}
            className="px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            مسح البيانات
          </button>
        </div>
      </div>
    </div>
  )
}

export default AuthDebug
