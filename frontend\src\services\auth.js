import { apiService, authUtils } from './api'

// إعادة تصدير authUtils
export { authUtils } from './api'

// خدمة المصادقة
export const authService = {
  // تسجيل الدخول
  login: async (credentials) => {
    try {
      // تحويل البيانات إلى URLSearchParams كما يتوقع FastAPI
      const formData = new URLSearchParams()
      formData.append('username', credentials.username)
      formData.append('password', credentials.password)

      const response = await apiService.post('/auth/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      })

      // حفظ الرمز المميز
      authUtils.setToken(response.access_token)

      // الحصول على بيانات المستخدم
      const user = await authService.getCurrentUser()
      authUtils.setUser(user)

      return { user, token: response.access_token }
    } catch (error) {
      throw error
    }
  },

  // تسجيل مستخدم جديد
  register: async (userData) => {
    try {
      const response = await apiService.post('/auth/register', userData)
      return response
    } catch (error) {
      throw error
    }
  },

  // تسجيل الخروج
  logout: () => {
    authUtils.removeToken()
    window.location.href = '/login'
  },

  // الحصول على المستخدم الحالي
  getCurrentUser: async () => {
    try {
      const response = await apiService.get('/auth/me')
      return response
    } catch (error) {
      throw error
    }
  },

  // تغيير كلمة المرور
  changePassword: async (passwordData) => {
    try {
      const response = await apiService.post('/auth/change-password', passwordData)
      return response
    } catch (error) {
      throw error
    }
  },

  // التحقق من حالة المصادقة
  checkAuth: async () => {
    try {
      if (!authUtils.isAuthenticated()) {
        return false
      }

      // التحقق من صحة الرمز المميز
      const user = await authService.getCurrentUser()
      authUtils.setUser(user)
      return true
    } catch (error) {
      authUtils.removeToken()
      return false
    }
  },

  // تحديث بيانات المستخدم
  updateProfile: async (userData) => {
    try {
      const response = await apiService.put('/auth/me', userData)
      authUtils.setUser(response)
      return response
    } catch (error) {
      throw error
    }
  },
}
